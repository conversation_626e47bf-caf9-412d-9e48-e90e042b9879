[project]
name = "mason"
version = "0.0.0"
description = "Bricklayer's <PERSON>"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.11"
readme = "README.md"
dependencies = []

[tool.pytest.ini_options]
addopts = "--cov=packages --cov-report=term-missing"
testpaths = [
    "packages"
]

[tool.uv]
package = false
cache-keys = [{ file = "pyproject.toml" }, { git = true }]
dev-dependencies = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov>=5.0"
]

[tool.uv.sources]
pantheon = { workspace = true }
dashdonald = { workspace = true }

[tool.uv.workspace]
members = [
    "packages/*",
    "projects/*"
]

[tool.hatch.build.targest.wheel]
packages = []

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
