repos:
  - repo: https://github.com/pycqa/autoflake
    rev: v1.4
    hooks:
      - id: autoflake
        name: autoflake-remove-unused-imports
        args: ["--remove-all-unused-imports", "--recursive", "--verbose"]
        additional_dependencies: ["autoflake==1.4"]

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort
        args: ["--verbose", "--profile", "black"]
        language_version: python3

  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        args: ["--verbose"]
        language_version: python3

  - repo: https://github.com/Quantco/pre-commit-mirrors-insert-license
    rev: v1.3.0
    hooks:
      - id: insert-license
        args:
          - --license-file=.license-header.txt
          - --comment-style=python
        files: \.py$
