# © 2025 Bricklayer.ai. All rights reserved.
"""
Example usage of the Plugin Agent.

This demonstrates how <PERSON><PERSON><PERSON> would use the Plugin Agent to generate
Bricklayer plugins from OpenAPI specifications.
"""

from blai.pantheon.plugin_agent.processor import PluginAgentProcessor, PluginAgentProcessingRequest
from blai.pantheon.plugin_agent.models import (
    PluginGenerationRequest, 
    OpenAPISpec, 
    UserIntent, 
    FileFormat,
    IntentCategory
)


def example_virustotal_plugin():
    """Example: Generate a VirusTotal plugin for IP intelligence."""
    
    # Sample OpenAPI spec content (truncated for example)
    virustotal_spec = """
openapi: 3.0.0
info:
  title: VirusTotal API
  version: 3.0
  description: VirusTotal API for threat intelligence
servers:
  - url: https://www.virustotal.com/api/v3
paths:
  /ip_addresses/{ip}:
    get:
      summary: Get IP address report
      parameters:
        - name: ip
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: IP address report
  /domains/{domain}:
    get:
      summary: Get domain report
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Domain report
"""

    # Create the request
    request = PluginAgentProcessingRequest(
        plugin_request=PluginGenerationRequest(
            openapi_spec=OpenAPISpec(
                content=virustotal_spec,
                format=FileFormat.OPENAPI_YAML,
                filename="virustotal_api.yaml"
            ),
            user_intent=UserIntent(
                description="I want IP intelligence capabilities to check IP reputation and get threat data",
                category=IntentCategory.IP_INTELLIGENCE,
                keywords=["ip", "intelligence", "reputation", "threat"]
            ),
            request_id="example-001"
        )
    )
    
    # Process the request
    processor = PluginAgentProcessor()
    
    if processor.validate_request(request):
        response = processor(request)
        
        print("Plugin Generation Results:")
        print("=" * 50)
        print(response.summary())
        
        if response.is_successful:
            print("\n✅ Plugin generation successful!")
            print(f"Quality Score: {response.quality_score:.2%}")
        else:
            print("\n❌ Plugin generation had issues:")
            for issue in response.validation.issues:
                print(f"  - {issue.severity}: {issue.message}")
    
    else:
        print("❌ Invalid request")


def example_sentinelone_plugin():
    """Example: Generate a SentinelOne plugin for threat hunting."""
    
    # This would be the actual SentinelOne OpenAPI spec
    sentinelone_spec = """
openapi: 3.0.0
info:
  title: SentinelOne API
  version: 2.1
  description: SentinelOne Management Console API
# ... (full spec would be here)
"""

    request = PluginAgentProcessingRequest(
        plugin_request=PluginGenerationRequest(
            openapi_spec=OpenAPISpec(
                content=sentinelone_spec,
                format=FileFormat.OPENAPI_YAML,
                filename="sentinelone_api.yaml"
            ),
            user_intent=UserIntent(
                description="I need threat hunting capabilities to search for IOCs and investigate threats",
                category=IntentCategory.THREAT_HUNTING,
                keywords=["threat", "hunting", "ioc", "investigation", "search"]
            ),
            request_id="example-002"
        )
    )
    
    processor = PluginAgentProcessor()
    response = processor(request)
    
    return response


if __name__ == "__main__":
    # Run the example
    example_virustotal_plugin()
