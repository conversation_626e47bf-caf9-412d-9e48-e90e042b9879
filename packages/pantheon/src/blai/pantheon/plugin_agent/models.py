# © 2025 Bricklayer.ai. All rights reserved.
"""
Data models for Plugin Agent processing.

Defines input/output structures for OpenAPI specs, user intent,
and Bricklayer plugin generation.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class FileFormat(str, Enum):
    """Supported input file formats."""
    OPENAPI_YAML = "openapi_yaml"
    OPENAPI_JSON = "openapi_json"
    POSTMAN_JSON = "postman_json"  # Future enhancement
    UNSTRUCTURED = "unstructured"  # Future enhancement


class IntentCategory(str, Enum):
    """Categories of user intent for plugin generation."""
    IP_INTELLIGENCE = "ip_intelligence"
    DOMAIN_ANALYSIS = "domain_analysis"
    FILE_ANALYSIS = "file_analysis"
    URL_SCANNING = "url_scanning"
    THREAT_HUNTING = "threat_hunting"
    VULNERABILITY_SCANNING = "vulnerability_scanning"
    INCIDENT_RESPONSE = "incident_response"
    GENERAL = "general"


class OpenAPISpec(BaseModel):
    """
    Represents an uploaded OpenAPI specification.
    """
    content: str = Field(..., description="Raw content of the OpenAPI spec")
    format: FileFormat = Field(..., description="Format of the uploaded file")
    filename: str = Field(..., description="Original filename")
    upload_timestamp: datetime = Field(default_factory=datetime.now)
    
    def summary(self) -> str:
        """Generate a summary for LLM processing."""
        return f"""
[OpenAPI Specification]
Filename: {self.filename}
Format: {self.format.value}
Upload Time: {self.upload_timestamp}
Content Length: {len(self.content)} characters

[Content]
{self.content}
[/Content]
"""


class UserIntent(BaseModel):
    """
    Represents user's intent/goal for plugin generation.
    """
    description: str = Field(..., description="User's intent description")
    category: Optional[IntentCategory] = Field(None, description="Categorized intent")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    priority_endpoints: List[str] = Field(default_factory=list, description="Specific endpoints mentioned")
    
    def summary(self) -> str:
        """Generate a summary for LLM processing."""
        return f"""
[User Intent]
Description: {self.description}
Category: {self.category.value if self.category else 'Not categorized'}
Keywords: {', '.join(self.keywords) if self.keywords else 'None'}
Priority Endpoints: {', '.join(self.priority_endpoints) if self.priority_endpoints else 'None'}
"""


# Placeholder for Bricklayer Plugin Specification
# TODO: Replace with actual Bricklayer plugin spec structure
class BricklayerPluginEndpoint(BaseModel):
    """Individual endpoint in a Bricklayer plugin."""
    name: str = Field(..., description="Endpoint name")
    method: str = Field(..., description="HTTP method")
    path: str = Field(..., description="API path")
    description: str = Field(..., description="Endpoint description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Endpoint parameters")
    response_schema: Dict[str, Any] = Field(default_factory=dict, description="Response schema")


class BricklayerPlugin(BaseModel):
    """
    Bricklayer plugin specification format.
    TODO: Update this with the actual Bricklayer plugin spec structure.
    """
    name: str = Field(..., description="Plugin name")
    version: str = Field(default="1.0.0", description="Plugin version")
    description: str = Field(..., description="Plugin description")
    provider: str = Field(..., description="Service provider name")
    category: str = Field(..., description="Plugin category")
    endpoints: List[BricklayerPluginEndpoint] = Field(..., description="Plugin endpoints")
    authentication: Dict[str, Any] = Field(default_factory=dict, description="Auth configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    def summary(self) -> str:
        """Generate a summary for validation."""
        return f"""
[Bricklayer Plugin]
Name: {self.name}
Provider: {self.provider}
Category: {self.category}
Endpoints: {len(self.endpoints)}
Description: {self.description}
"""


class PluginGenerationRequest(BaseModel):
    """
    Main input for plugin generation processing.
    """
    openapi_spec: OpenAPISpec = Field(..., description="Uploaded OpenAPI specification")
    user_intent: UserIntent = Field(..., description="User's intent/goal")
    request_id: str = Field(..., description="Unique request identifier")
    
    def summary(self) -> str:
        """Generate comprehensive summary for LLM processing."""
        return f"""
[Plugin Generation Request]
Request ID: {self.request_id}

{self.openapi_spec.summary()}

{self.user_intent.summary()}
"""


class ValidationIssue(BaseModel):
    """Individual validation issue."""
    severity: str = Field(..., description="Issue severity: error, warning, info")
    message: str = Field(..., description="Issue description")
    location: Optional[str] = Field(None, description="Location in spec where issue occurs")
    suggestion: Optional[str] = Field(None, description="Suggested fix")


class ProcessingMetadata(BaseModel):
    """Metadata about the processing workflow."""
    processing_time: float = Field(..., description="Total processing time in seconds")
    llm_calls: int = Field(..., description="Number of LLM calls made")
    tokens_used: int = Field(default=0, description="Total tokens consumed")
    workflow_steps: List[str] = Field(default_factory=list, description="Steps executed")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
