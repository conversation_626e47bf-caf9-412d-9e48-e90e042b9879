# © 2025 Bricklayer.ai. All rights reserved.
"""
Data models for Plugin Agent processing.

Defines input/output structures for plugin generation from OpenAPI specs.
"""

from datetime import datetime
from typing import Any, Dict
from pydantic import BaseModel, Field


class PluginGenerationRequest(BaseModel):
    """
    Main input for plugin generation processing.
    This is what Django will send to our processor.
    """
    openapi_yaml_content: str = Field(..., description="Raw YAML content of the OpenAPI spec")
    user_intent: str = Field(..., description="User's intent/goal description")
    request_id: str = Field(..., description="Unique request identifier")
    
    def summary(self) -> str:
        """
        Generate comprehensive summary for LLM processing.
        Following agent_debrief pattern of converting structured data to readable text.
        """
        return f"""
[Plugin Generation Request]
Request ID: {self.request_id}

[User Intent]
{self.user_intent}

[OpenAPI Specification Content]
{self.openapi_yaml_content}
[/OpenAPI Specification Content]
"""


# TODO: Replace this placeholder with your actual Bricklayer plugin specification
class BricklayerPluginSpec(BaseModel):
    """
    Bricklayer plugin specification format.
    
    NOTE: This is a placeholder structure. 
    Please provide the actual Bricklayer plugin specification format
    to replace this model.
    """
    name: str = Field(..., description="Plugin name")
    version: str = Field(default="1.0.0", description="Plugin version")
    description: str = Field(..., description="Plugin description")
    provider: str = Field(..., description="Service provider name (e.g., VirusTotal, SentinelOne)")
    
    # Placeholder fields - replace with actual Bricklayer spec structure
    endpoints: Dict[str, Any] = Field(default_factory=dict, description="Plugin endpoints")
    authentication: Dict[str, Any] = Field(default_factory=dict, description="Auth configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    def summary(self) -> str:
        """Generate a summary for validation and display."""
        return f"""
[Generated Bricklayer Plugin]
Name: {self.name}
Provider: {self.provider}
Version: {self.version}
Description: {self.description}
Endpoints: {len(self.endpoints) if isinstance(self.endpoints, dict) else 'N/A'}
"""
