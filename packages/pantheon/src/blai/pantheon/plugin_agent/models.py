# © 2025 Bricklayer.ai. All rights reserved.
"""
Data models for Plugin Agent processing.

Defines input/output structures for plugin generation from OpenAPI specs.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Literal
from enum import Enum
from pydantic import BaseModel, Field, SecretStr


class PluginGenerationRequest(BaseModel):
    """
    Main input for plugin generation processing.
    This is what Django will send to our processor.
    """
    openapi_yaml_content: str = Field(..., description="Raw YAML content of the OpenAPI spec")
    user_intent: str = Field(..., description="User's intent/goal description")
    request_id: str = Field(..., description="Unique request identifier")
    
    def summary(self) -> str:
        """
        Generate comprehensive summary for LLM processing.
        Converting structured data to readable text.
        """
        return f"""
[Plugin Generation Request]
Request ID: {self.request_id}

[User Intent]
{self.user_intent}

[OpenAPI Specification Content]
{self.openapi_yaml_content}
[/OpenAPI Specification Content]
"""


# Bricklayer Plugin Specification Models
# Based on the actual Bricklayer plugin specification

class ApiCredentialType(str, Enum):
    ApiKey = "api_key"
    BasicAuth = "basic_auth"


class ApiKeyData(BaseModel):
    key: SecretStr = Field(description="The API key")
    header: str = Field(description="The header name for the API key")


class ApiCredentialsSpec(BaseModel):
    type: ApiCredentialType = Field(
        description="Type of credentials: api key / basic auth",
        default=ApiCredentialType.ApiKey,
    )
    data: Optional[ApiKeyData] = Field(
        description="The data for this credentials object",
        default=None,
    )


class Parameter(BaseModel):
    name: str
    type: Literal["str", "int", "bool", "float", "list", "dict", "file", "object"] = "str"
    required: bool = False
    location: Literal["query", "header", "body", "path", "json"] = "query"
    default: Optional[Union[str, int, bool, float, list, dict]] = None
    definition: Optional[str] = None


class Endpoint(BaseModel):
    path: str
    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"] = "GET"
    parameters: List[Parameter] = Field(default_factory=list)
    headers: Optional[Dict[str, str]] = Field(default_factory=dict)
    auth_required: bool = False
    auth_location: Literal["header", "query"] = "header"
    auth_header_name: Optional[str] = None
    description: Optional[str] = None
    content_type: Literal[
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "application/vnd.github.v3.raw"
    ] = "application/json"


class PluginV2(BaseModel):
    base_url: Optional[str] = Field(
        description="The base URL of the API",
        default=None,
    )
    endpoints: List[Endpoint] = Field(
        default_factory=list,
        description="List of endpoints",
    )


class ApiPluginSpec(BaseModel):
    """
    Main Bricklayer plugin specification (V2 format only).
    """
    name: str = Field(
        description="The name of the tool. Will be used by the LLM during selection"
    )
    description: str = Field(
        description="The description of the tool. Will be used by the LLM during selection"
    )
    credentials: Optional[ApiCredentialsSpec] = None
    spec: Optional[PluginV2] = Field(
        description="Specification for the plugin",
        default=None,
    )
    auth_type: Optional[str] = Field(
        description="Type of authentication",
        default=None,
    )

    def summary(self) -> str:
        """Generate a summary for validation and display."""
        endpoint_count = len(self.spec.endpoints) if self.spec and self.spec.endpoints else 0
        return f"""
[Generated Bricklayer Plugin]
Name: {self.name}
Description: {self.description}
Auth Type: {self.auth_type or 'None'}
Base URL: {self.spec.base_url if self.spec else 'None'}
Endpoints: {endpoint_count}
"""
