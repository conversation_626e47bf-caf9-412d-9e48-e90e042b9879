# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin output render target model.
"""

from typing import Dict, Any, Literal
from pydantic import BaseModel, Field

from blai.pantheon.plugin_agent.models import BricklayerPlugin


class PluginOutput(BaseModel):
    """
    Render target for generated Bricklayer plugin output.
    
    This model represents the final plugin specification that will be
    returned to the Django service and can be used to create an actual
    Bricklayer plugin.
    """
    kind: Literal["PluginOutput"] = "PluginOutput"
    
    plugin_spec: BricklayerPlugin = Field(
        ..., 
        description="Generated Bricklayer plugin specification"
    )
    
    generation_summary: str = Field(
        ..., 
        description="Human-readable summary of what was generated"
    )
    
    matched_endpoints: int = Field(
        ..., 
        description="Number of endpoints that matched the user intent"
    )
    
    total_endpoints_analyzed: int = Field(
        ..., 
        description="Total number of endpoints in the original OpenAPI spec"
    )
    
    confidence_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Confidence score for the generated plugin (0.0 to 1.0)"
    )
    
    intent_match_details: Dict[str, Any] = Field(
        default_factory=dict,
        description="Details about how user intent was matched to endpoints"
    )
    
    def summary(self) -> str:
        """Generate a summary for display."""
        return f"""
Plugin Generated: {self.plugin_spec.name}
Provider: {self.plugin_spec.provider}
Endpoints Included: {self.matched_endpoints}/{self.total_endpoints_analyzed}
Confidence: {self.confidence_score:.2%}
Summary: {self.generation_summary}
"""
