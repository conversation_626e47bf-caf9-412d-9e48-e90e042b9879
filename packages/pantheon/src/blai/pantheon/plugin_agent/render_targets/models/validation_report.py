# © 2025 Bricklayer.ai. All rights reserved.
"""
Validation report render target model.
"""

from typing import List, Literal
from pydantic import BaseModel, Field

from blai.pantheon.plugin_agent.models import ValidationIssue


class ValidationReport(BaseModel):
    """
    Render target for plugin validation results.
    
    This model represents the validation feedback for the generated plugin,
    including any issues found and suggestions for improvement.
    """
    kind: Literal["ValidationReport"] = "ValidationReport"
    
    is_valid: bool = Field(
        ..., 
        description="Whether the generated plugin passes validation"
    )
    
    validation_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Overall validation score (0.0 to 1.0)"
    )
    
    issues: List[ValidationIssue] = Field(
        default_factory=list,
        description="List of validation issues found"
    )
    
    passed_checks: List[str] = Field(
        default_factory=list,
        description="List of validation checks that passed"
    )
    
    failed_checks: List[str] = Field(
        default_factory=list,
        description="List of validation checks that failed"
    )
    
    recommendations: List[str] = Field(
        default_factory=list,
        description="Recommendations for improving the plugin"
    )
    
    compliance_details: dict = Field(
        default_factory=dict,
        description="Details about Bricklayer spec compliance"
    )
    
    def summary(self) -> str:
        """Generate a summary for display."""
        error_count = len([i for i in self.issues if i.severity == "error"])
        warning_count = len([i for i in self.issues if i.severity == "warning"])
        
        return f"""
Validation Status: {'PASSED' if self.is_valid else 'FAILED'}
Validation Score: {self.validation_score:.2%}
Issues Found: {error_count} errors, {warning_count} warnings
Passed Checks: {len(self.passed_checks)}
Failed Checks: {len(self.failed_checks)}
Recommendations: {len(self.recommendations)}
"""
    
    @property
    def error_count(self) -> int:
        """Count of error-level issues."""
        return len([i for i in self.issues if i.severity == "error"])
    
    @property
    def warning_count(self) -> int:
        """Count of warning-level issues."""
        return len([i for i in self.issues if i.severity == "warning"])
    
    @property
    def info_count(self) -> int:
        """Count of info-level issues."""
        return len([i for i in self.issues if i.severity == "info"])
