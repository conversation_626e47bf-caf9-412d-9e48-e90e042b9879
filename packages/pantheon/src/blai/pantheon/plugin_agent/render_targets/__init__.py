# © 2025 Bricklayer.ai. All rights reserved.
"""
Render targets for Plugin Agent output formats.

Defines the response structure that the Plugin Agent returns to Django.
"""

from pydantic import BaseModel, Field

from blai.pantheon.plugin_agent.models import ApiPluginSpec


class PluginAgentResponse(BaseModel):
    """
    Main response format for Plugin Agent processing.
    
    This is what Django receives after plugin generation is complete.
    Contains the generated Bricklayer plugin specification.
    """
    id: str = Field(..., description="Unique identifier for this plugin generation")
    plugin: ApiPluginSpec = Field(..., description="Generated Bricklayer plugin specification")
    
    def summary(self) -> str:
        """Generate a summary of the plugin generation results."""
        return f"""
Plugin Generation Results (ID: {self.id})

{self.plugin.summary()}
"""
    
    @property
    def is_successful(self) -> bool:
        """Check if plugin generation was successful."""
        return (
            self.plugin is not None and 
            self.plugin.name is not None and
            self.plugin.spec is not None and
            len(self.plugin.spec.endpoints) > 0
        )


__all__ = [
    "PluginAgentResponse",
]
