# © 2025 Bricklayer.ai. All rights reserved.
"""
Render targets for Plugin Agent output formats.
"""

from typing import List, Union
from pydantic import BaseModel, Field

from .models.plugin_output import PluginOutput
from .models.validation_report import ValidationReport

# Union of all possible output elements
RenderElement = Union[PluginOutput, ValidationReport]

class PluginAgentResponse(BaseModel):
    """
    Main response format for Plugin Agent processing.
    Contains the generated plugin and validation information.
    """
    id: str = Field(..., description="Unique identifier for this plugin generation")
    plugin: PluginOutput = Field(..., description="Generated Bricklayer plugin specification")
    validation: ValidationReport = Field(..., description="Validation results and feedback")
    metadata: dict = Field(default_factory=dict, description="Additional processing metadata")

    def summary(self) -> str:
        """Generate a summary of the plugin generation results."""
        return f"""
Plugin Generation Results (ID: {self.id})

{self.plugin.summary()}

{self.validation.summary()}

Processing Metadata:
{self.metadata}
"""

    @property
    def is_successful(self) -> bool:
        """Check if plugin generation was successful."""
        return (
            self.validation.is_valid and
            self.plugin.confidence_score >= 0.5 and
            self.validation.error_count == 0
        )

    @property
    def quality_score(self) -> float:
        """Calculate overall quality score."""
        return (self.plugin.confidence_score + self.validation.validation_score) / 2.0

__all__ = [
    "PluginAgentResponse",
    "PluginOutput",
    "ValidationReport",
    "RenderElement"
]
