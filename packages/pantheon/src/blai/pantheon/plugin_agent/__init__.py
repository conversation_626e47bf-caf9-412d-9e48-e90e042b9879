# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Agent - Generates Bricklayer plugins from OpenAPI specifications.

This agent analyzes OpenAPI YAML specs and user intent to generate compliant
Bricklayer plugin specifications for cybersecurity service providers.

Usage:
    from blai.pantheon.plugin_agent import PluginAgentProcessor
    
    processor = PluginAgentProcessor()
    response = processor(request)
"""

from .processor import PluginAgentProcessor
from .models import PluginGenerationRequest, ApiPluginSpec
from .render_targets import PluginAgentResponse

__all__ = [
    "PluginAgentProcessor",
    "PluginGenerationRequest",
    "ApiPluginSpec",
    "PluginAgentResponse"
]
