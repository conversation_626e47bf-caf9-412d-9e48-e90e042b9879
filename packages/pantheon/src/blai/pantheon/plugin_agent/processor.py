# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Agent Processor - Main entry point for plugin generation.

This processor orchestrates the generation of Bricklayer plugins from
OpenAPI YAML specifications and user intent.
"""

import uuid
from textwrap import dedent
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


from blai.pantheon.plugin_agent.models import PluginGenerationRequest
from blai.pantheon.plugin_agent.render_targets import PluginAgentResponse
from blai.pantheon.agents.configurable.supervisors.plugin_agent import PluginAgent
from blai.pantheon.settings import AI_SETTINGS, AzureOpenAISettings


class PluginAgentProcessorSettings(BaseSettings):
    ai: AI_SETTINGS = Field(default_factory=AzureOpenAISettings)


class PluginAgentProcessor(BaseModel):
    """
    Main processor for plugin generation.
    
    This processor takes a plugin generation request and produces a
    Bricklayer plugin specification by analyzing the OpenAPI YAML
    and matching it to user intent.
    """

    def __call__(self, request: PluginGenerationRequest) -> PluginAgentResponse:
        """
        Process a plugin generation request.
        
        Args:
            request: The processing request containing OpenAPI YAML and user intent
            
        Returns:
            PluginAgentResponse with generated plugin specification
        """

        if self.settings is None:
            self.settings = PluginAgentProcessorSettings()
        
        # Generate unique ID for this processing request if not provided
        processing_id = request.request_id or str(uuid.uuid4())
        
        # Create the prompt for the plugin agent using the request summary
        prompt = dedent(f"""
        Generate a Bricklayer plugin specification from the provided OpenAPI YAML specification and user intent.
        
        TASK:
        - Analyze the OpenAPI YAML specification thoroughly
        - Understand the user's intent and match it to relevant endpoints
        - Generate a compliant Bricklayer plugin specification in V2 format
        - Focus on API key authentication
        - Include only endpoints relevant to the user's intent
        
        {request.summary()}
        """)

        # Invoke the plugin agent supervisor
        resp = PluginAgent.create().invoke({"user_prompt": prompt})["structured_response"]
        
        # Ensure the response has the processing ID
        if hasattr(resp, 'id'):
            resp.id = processing_id
        
        return resp
