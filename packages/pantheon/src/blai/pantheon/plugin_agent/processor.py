# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Agent Processor - Main entry point for plugin generation.

This processor orchestrates the generation of Bricklayer plugins from
OpenAPI specifications and user intent.
"""

import uuid
from textwrap import dedent
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from blai.pantheon.plugin_agent.models import PluginGenerationRequest
from blai.pantheon.plugin_agent.render_targets import PluginAgentResponse
from blai.pantheon.agents.configurable.supervisors.plugin_agent import PluginAgent
from blai.pantheon.settings import AI_SETTINGS, AzureOpenAISettings


class PluginAgentProcessorSettings(BaseSettings):
    """Settings for the Plugin Agent processor."""
    ai: AI_SETTINGS = Field(default_factory=AzureOpenAISettings)
    max_processing_time: int = Field(default=300, description="Maximum processing time in seconds")
    enable_validation: bool = Field(default=True, description="Whether to validate generated plugins")
    strict_mode: bool = Field(default=False, description="Whether to use strict validation")


class PluginAgentProcessingRequest(BaseModel):
    """
    Request format for plugin generation processing.
    This is what Django will send to the processor.
    """
    plugin_request: PluginGenerationRequest = Field(..., description="Plugin generation request data")


class PluginAgentProcessor(BaseModel):
    """
    Main processor for plugin generation.
    
    This processor takes a plugin generation request and produces a
    Bricklayer plugin specification by analyzing the OpenAPI spec
    and matching it to user intent.
    """
    settings: Optional[PluginAgentProcessorSettings] = None

    def __call__(self, request: PluginAgentProcessingRequest) -> PluginAgentResponse:
        """
        Process a plugin generation request.
        
        Args:
            request: The processing request containing OpenAPI spec and user intent
            
        Returns:
            PluginAgentResponse with generated plugin and validation results
        """
        if self.settings is None:
            self.settings = PluginAgentProcessorSettings()
        
        # Generate unique ID for this processing request
        processing_id = str(uuid.uuid4())
        
        # Create the prompt for the plugin agent
        prompt = dedent(f"""
        Generate a Bricklayer plugin specification from the provided OpenAPI specification and user intent.
        
        PROCESSING REQUIREMENTS:
        - Parse and analyze the OpenAPI specification thoroughly
        - Understand the user's intent and match it to relevant endpoints
        - Generate a compliant Bricklayer plugin specification
        - Validate the generated plugin for quality and compliance
        - Provide detailed feedback and recommendations
        
        QUALITY STANDARDS:
        - Plugin must follow Bricklayer specification exactly
        - Include only endpoints relevant to user intent
        - Ensure proper authentication configuration
        - Provide clear descriptions and metadata
        - Validate all parameters and response schemas
        
        PROCESSING ID: {processing_id}
        
        {request.plugin_request.summary()}
        """)

        # Invoke the plugin agent supervisor
        resp = PluginAgent.create().invoke({"user_prompt": prompt})["structured_response"]
        
        # Ensure the response has the processing ID
        if hasattr(resp, 'id'):
            resp.id = processing_id
        
        return resp

    def validate_request(self, request: PluginAgentProcessingRequest) -> bool:
        """
        Validate the incoming request.
        
        Args:
            request: The processing request to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Basic validation
            if not request.plugin_request.openapi_spec.content.strip():
                return False
            
            if not request.plugin_request.user_intent.description.strip():
                return False
            
            # Could add more sophisticated validation here
            return True
            
        except Exception:
            return False

    def get_supported_formats(self) -> list:
        """
        Get list of supported input formats.
        
        Returns:
            List of supported file formats
        """
        return [
            "openapi_yaml",
            "openapi_json",
            # Future enhancements:
            # "postman_json",
            # "unstructured"
        ]

    def get_supported_categories(self) -> list:
        """
        Get list of supported intent categories.
        
        Returns:
            List of supported intent categories
        """
        return [
            "ip_intelligence",
            "domain_analysis", 
            "file_analysis",
            "url_scanning",
            "threat_hunting",
            "vulnerability_scanning",
            "incident_response",
            "general"
        ]
