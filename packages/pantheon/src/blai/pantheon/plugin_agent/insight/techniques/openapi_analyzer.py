# © 2025 Bricklayer.ai. All rights reserved.
"""
OpenAPI specification analysis techniques.
"""

from typing import Dict, List, Any
from pydantic import BaseModel, Field


class ParsedEndpoint(BaseModel):
    """Represents a parsed endpoint from OpenAPI spec."""
    path: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    operation_id: str = Field(..., description="OpenAPI operation ID")
    summary: str = Field(..., description="Endpoint summary")
    description: str = Field(..., description="Detailed description")
    parameters: List[Dict[str, Any]] = Field(default_factory=list, description="Endpoint parameters")
    responses: Dict[str, Any] = Field(default_factory=dict, description="Response definitions")
    tags: List[str] = Field(default_factory=list, description="OpenAPI tags")
    security: List[Dict[str, Any]] = Field(default_factory=list, description="Security requirements")


class ParsedOpenAPISpec(BaseModel):
    """Represents a fully parsed OpenAPI specification."""
    title: str = Field(..., description="API title")
    version: str = Field(..., description="API version")
    description: str = Field(..., description="API description")
    base_url: str = Field(..., description="Base URL/server")
    endpoints: List[ParsedEndpoint] = Field(..., description="All parsed endpoints")
    security_schemes: Dict[str, Any] = Field(default_factory=dict, description="Security schemes")
    components: Dict[str, Any] = Field(default_factory=dict, description="Reusable components")
    tags: List[Dict[str, str]] = Field(default_factory=list, description="API tags with descriptions")
    
    def get_endpoints_by_tag(self, tag: str) -> List[ParsedEndpoint]:
        """Get all endpoints with a specific tag."""
        return [ep for ep in self.endpoints if tag in ep.tags]
    
    def search_endpoints(self, keywords: List[str]) -> List[ParsedEndpoint]:
        """Search endpoints by keywords in path, summary, or description."""
        results = []
        for endpoint in self.endpoints:
            text_to_search = f"{endpoint.path} {endpoint.summary} {endpoint.description}".lower()
            if any(keyword.lower() in text_to_search for keyword in keywords):
                results.append(endpoint)
        return results


class OpenAPIAnalysisResult(BaseModel):
    """Result of OpenAPI specification analysis."""
    parsed_spec: ParsedOpenAPISpec = Field(..., description="Parsed OpenAPI specification")
    analysis_summary: str = Field(..., description="Summary of the analysis")
    endpoint_categories: Dict[str, List[str]] = Field(
        default_factory=dict, 
        description="Endpoints categorized by functionality"
    )
    security_analysis: Dict[str, Any] = Field(
        default_factory=dict,
        description="Analysis of security schemes and requirements"
    )
    complexity_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Complexity score of the API (0.0 = simple, 1.0 = complex)"
    )
    recommendations: List[str] = Field(
        default_factory=list,
        description="Recommendations for plugin generation"
    )
