# © 2025 Bricklayer.ai. All rights reserved.
"""
Intent matching and analysis techniques.
"""

from typing import Dict, List, Any
from pydantic import BaseModel, Field

from blai.pantheon.plugin_agent.models import IntentCategory
from .openapi_analyzer import ParsedEndpoint


class EndpointMatch(BaseModel):
    """Represents a match between user intent and an endpoint."""
    endpoint: ParsedEndpoint = Field(..., description="The matched endpoint")
    relevance_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Relevance score for this endpoint (0.0 to 1.0)"
    )
    match_reasons: List[str] = Field(
        default_factory=list,
        description="Reasons why this endpoint matches the intent"
    )
    matched_keywords: List[str] = Field(
        default_factory=list,
        description="Keywords from intent that matched this endpoint"
    )
    priority: int = Field(
        ...,
        ge=1,
        description="Priority ranking (1 = highest priority)"
    )


class IntentAnalysisResult(BaseModel):
    """Result of intent analysis and matching."""
    categorized_intent: IntentCategory = Field(..., description="Categorized user intent")
    extracted_keywords: List[str] = Field(..., description="Keywords extracted from intent")
    matched_endpoints: List[EndpointMatch] = Field(..., description="Endpoints that match the intent")
    confidence_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0,
        description="Confidence in the intent analysis (0.0 to 1.0)"
    )
    analysis_summary: str = Field(..., description="Summary of the intent analysis")
    suggested_categories: List[str] = Field(
        default_factory=list,
        description="Alternative categories that might also apply"
    )
    filtering_criteria: Dict[str, Any] = Field(
        default_factory=dict,
        description="Criteria used to filter and rank endpoints"
    )
    
    def get_top_matches(self, limit: int = 10) -> List[EndpointMatch]:
        """Get the top N endpoint matches by relevance score."""
        return sorted(
            self.matched_endpoints, 
            key=lambda x: (-x.relevance_score, x.priority)
        )[:limit]
    
    def get_high_confidence_matches(self, threshold: float = 0.7) -> List[EndpointMatch]:
        """Get endpoint matches above a confidence threshold."""
        return [match for match in self.matched_endpoints if match.relevance_score >= threshold]


class IntentMatchingStrategy(BaseModel):
    """Configuration for intent matching strategy."""
    keyword_weight: float = Field(default=0.4, description="Weight for keyword matching")
    semantic_weight: float = Field(default=0.3, description="Weight for semantic similarity")
    tag_weight: float = Field(default=0.2, description="Weight for OpenAPI tag matching")
    path_weight: float = Field(default=0.1, description="Weight for path pattern matching")
    min_relevance_threshold: float = Field(default=0.3, description="Minimum relevance score to include")
    max_endpoints: int = Field(default=20, description="Maximum endpoints to include in plugin")
    
    def validate_weights(self) -> bool:
        """Validate that weights sum to 1.0."""
        total = self.keyword_weight + self.semantic_weight + self.tag_weight + self.path_weight
        return abs(total - 1.0) < 0.01
