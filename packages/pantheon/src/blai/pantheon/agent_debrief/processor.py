from textwrap import dedent
from typing import Optional

from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings

from blai.pantheon.agent_debrief.models import ProcedureRunDetails
from blai.pantheon.agent_debrief.render_targets import AgentDebriefResponse
from blai.pantheon.agents.configurable.supervisors.agent_debrief import AgentDebrief
from blai.pantheon.settings import AzureOpenAISettings, AI_SETTINGS


class AgentDebriefProcessorSettings(BaseSettings):
    ai: AI_SETTINGS = Field(default_factory=AzureOpenAISettings)


class AgentDebriefProcessingRequest(BaseModel):
    procedure_run_output: ProcedureRunDetails

class AgentDebriefProcessor(BaseModel):
    settings: Optional[AgentDebriefProcessorSettings] = None

    def __call__(self, request: AgentDebriefProcessingRequest) -> AgentDebriefResponse:
        if self.settings is None:
            self.settings = AgentDebriefProcessorSettings()
        prompt = dedent(f"""
        Read the following ProcedureRunDetails outcome and decide any if the outcomes have information that can be visualized 
        using the elements of the ProcedureOutcomeRequest. The goal is to visually convey the outcome of the below 
        procedure and the tasks.  Multiple of each kind of element can be used. If not, return an empty list for elements. 
        You must always perform analysis using the MitreAttackAnalyzer first.
        At minimum you must make: 
            1. Executive Summary (RichTextCard) 
            2. Key Findings (KeyValuePair or Table) 
            3. TimelineCard if any events have time information
        
        [ProcedureRunDetails]
        {request.procedure_run_output.summary()}
        [/ProcedureRunDetails]
        """)

        resp = AgentDebrief.create().invoke({"user_prompt": prompt})["structured_response"]

        return resp
