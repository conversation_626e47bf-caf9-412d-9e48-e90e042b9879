from typing import List

from pydantic import BaseModel
from pydantic_settings import BaseSettings

from blai.pantheon.agent_debrief.insight.techniques import InsightProcessorTechnique


class InsightProcessorSettings(BaseSettings):
    ...


class InsightProcessor(BaseModel):
    settings: InsightProcessorSettings
    processors: List[InsightProcessorTechnique]

    def __call__(self):
        ...


