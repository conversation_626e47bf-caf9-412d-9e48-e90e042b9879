from typing import Literal, Union
from typing_extensions import Annotated

from pydantic import BaseModel, Field, IPvAnyAddress, HttpUrl


class IndicatorOfCompromise(BaseModel):
    kind: str = Field(
        ...,
        description=(
            "The type of the IOC value. "
            "This should specify the category, such as 'domain', 'IP address', 'file hash', "
            "'URL', 'filename', 'registry key', or 'process name'. "
            "Used to help classify and contextualize the IOC during threat analysis."
        )
    )

    context: str = Field(
        ...,
        description=(
            "Human-readable explanation of the IOC's relevance in the security incident. "
            "Should describe how the IOC was used or observed, such as "
            "'Malicious domain contacted by PowerShell script' or "
            "'SHA256 hash of dropped executable'."
        )
    )

    value: str = Field(
        ...,
        description=(
            "The raw indicator value used for detection, such as an IP address, domain name, "
            "file hash, filename, or command string. "
            "This is the actionable element that can be matched in telemetry or threat intelligence feeds."
        )
    )
