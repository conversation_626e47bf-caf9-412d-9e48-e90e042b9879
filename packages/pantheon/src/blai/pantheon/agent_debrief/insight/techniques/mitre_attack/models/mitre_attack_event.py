from datetime import datetime
from typing import Literal, Optional, List

from pydantic import BaseModel, Field, HttpUrl

from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.indicators_of_compromise import \
    IndicatorOfCompromise
from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.process_execution import ProcessExecution


class MitreAttackEvent(BaseModel):
    kind: Literal["MitreAttackEvent"] = "MitreAttackEvent"
    event_id: str = Field(..., description="Unique identifier for the event")
    timestamp: datetime = Field(..., description="Timestamp of the event occurrence")

    technique_id: str = Field(..., description="MITRE ATT&CK technique ID (e.g., T1059.001)")
    technique_name: str = Field(..., description="Name of the ATT&CK technique")
    tactic_name: str = Field(..., description="Name of the ATT&CK tactic")

    actor: Optional[str] = Field(None, description="Threat actor name if known")
    target_system: str = Field(..., description="Hostname of the affected system")
    target_user: Optional[str] = Field(None, description="User account involved")

    detection_source: Optional[str] = Field(None, description="Tool or platform that detected the threat")
    severity: Optional[str] = Field(None, description="Severity rating (low, medium, high, critical)")

    description: Optional[str] = Field(None, description="Narrative of the attack event")
    references: Optional[List[str]] = Field(default_factory=list, description="Relevant documentation or MITRE links")

    iocs: Optional[List[IndicatorOfCompromise]] = Field(default_factory=list)
    process_execution: Optional[ProcessExecution] = None
