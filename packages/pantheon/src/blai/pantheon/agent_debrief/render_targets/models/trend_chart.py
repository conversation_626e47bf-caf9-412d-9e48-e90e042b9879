from datetime import datetime
from typing import Union, Optional, List, Dict, Literal

from pydantic import BaseModel

Numerical = Union[int, float]

TrendX = Union[str, Numerical, datetime]

class TrendChartRecord(BaseModel):
    id: Optional[str]
    x: TrendX
    y: Numerical
    label: str


class TrendChartSeries(BaseModel):
    label: str
    data: List[TrendX]


class TrendChartConfig(BaseModel):
    kind: Literal["TrendChartConfig"] = "TrendChartConfig"
    title: str
    series: List[TrendChartSeries]
    xLabels: List[str]
    # mapper: Dict[(x, y, label), id]  # This would be to allow FE to localy get ID for BE call by id