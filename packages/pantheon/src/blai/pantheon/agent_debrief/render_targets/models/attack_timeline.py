"""
{
  "title": "Indicators of Compromise",
  "data": [
    {
      "title": "Related Asset",
      "value": "hostname-1120",
      "type": "10.10.120" //I believe "type" should be some kind of enum
    },
    {
      "title": "Related User",
      "value": "jbuchanan",
      "type": "AcmeCo"
    }
  ],
  "steps": [
    {
      "label": "Initial Powershell Execution",
      "timestamp": "03-19-2025 17:49 PM"
    },
    {
      "label": "Malware.exe Downloaded",
      "timestamp": "03-19-2025 17:49 PM"
    }
  ]
}
"""
from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, Field

from blai.pantheon.agent_debrief.render_targets.models.kvp_list import KeyValuePairItemProps


class TimelineStep(BaseModel):
    """
    A single step in a timeline visualization, used to illustrate a sequence of events or actions
    in chronological order. Timeline steps are intended to be compact, ordered indicators of activity,
    useful for incident response, attack chains, investigations, and audit trails.

    Each step includes a human-readable label, an exact timestamp (from the source, not inferred),
    and optional contextual metadata (key-value pairs) to enrich the event.
    """

    label: str = Field(
        description="A concise description of the event or activity that occurred at this step."
    )
    timestamp: datetime = Field(
        description=(
            "The exact timestamp when the step occurred. This must correspond directly to a timestamp "
            "present in the source report or prompt. Timezone information should be preserved where applicable."
        )
    )


class TimelineCardProps(BaseModel):
    """
    A visualization card for rendering sequential events in a narrative timeline format.

    This structure is well-suited for telling stories of progression, such as attack lifecycles,
    user behavior sequences, remediation timelines, or audit logs. Each card has a title and a list
    of timestamped steps. The steps are expected to be sorted in ascending time order for clear visual flow.

    - If multiple steps share the same timestamp, they should be aggregated or grouped for visual clarity.
    - This card is complementary to tabular or rich text formats and is best used when temporal context is critical.
    """

    kind: Literal["TimelineCardProps"] = "TimelineCardProps"
    title: str = Field(
        description="Title of the timeline card. It should briefly describe the purpose or context of the timeline (e.g. 'Execution Chain')."
    )
    steps: List[TimelineStep] = Field(
        description=(
            "A list of TimelineStep objects, sorted in ascending order by timestamp. "
            "If multiple steps share the same timestamp, they should be combined or grouped during rendering. "
            "This chronological order is important for correct narrative and analytical flow."
        )
    )
    data: Optional[List[KeyValuePairItemProps]] = Field(
        default=None,
        description=(
            "Optional contextual information presented as key-value pairs. These may include attributes like "
            "IP address, username, process name, or other metadata associated with the steps."
        )
    )
