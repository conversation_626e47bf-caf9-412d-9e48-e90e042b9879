"""
{
  "title": "Indicators of Compromise",
  "items": [
    {
      "title": "Internal IP",
      "value": "**********",
      "type": "Malicious"
    },
    {
      "title": "Filename",
      "value": "malware.exe",
      "type": "Malicious"
    },
  ]
}
"""
from typing import List, Literal

from pydantic import BaseModel, Field


class KeyValuePairItemProps(BaseModel):
    """
    A single key-value pair item used to represent a structured data point, often part of a
    summary or indicators list. This format is useful for surfacing small, high-signal pieces
    of information such as IoCs, user accounts, filenames, or network addresses.

    The 'type' field can be used to indicate the contextual classification of the item (e.g.,
    'Malicious', 'Suspicious', 'Informational'), and may inform how it is styled or prioritized
    in the visualization.
    """

    title: str = Field(
        description="Label describing what the value represents (e.g., 'Filename', 'IP Address')."
    )
    value: str = Field(
        description="The actual value of the item (e.g., an IP address, file name, user account)."
    )
    type: str = Field(
        description=(
            "A high-level classification for the item, typically used to influence visual emphasis "
            "or interpretive meaning. Common values include 'Malicious', 'Suspicious', 'Benign', "
            "or other analyst-defined categories."
        )
    )


class KeyValuePairListProps(BaseModel):
    """
    A compact card-like visualization component designed to present a list of related key-value
    pairs. This format is especially useful for summarizing indicators of compromise (IoCs),
    metadata fields, or important observations that benefit from side-by-side labeling.

    This card helps communicate detailed, structured insight at a glance and is typically used
    in conjunction with narrative or timeline elements to tell a broader story.
    """

    kind: Literal["KeyValuePairListProps"] = "KeyValuePairListProps"
    title: str = Field(
        description="The title of the key-value list card, usually describing the collection (e.g., 'Indicators of Compromise')."
    )
    items: List[KeyValuePairItemProps] = Field(
        description="A list of key-value pair items to be displayed in the card."
    )
