from typing import List, Literal

from pydantic import BaseModel, Field

class RichText(BaseModel):
    """
    A flexible visualization element intended for displaying narrative content that cannot be effectively
    represented using structured formats like tables, timelines, or charts.

    This model is particularly suited for key insights, thematic summaries, conclusions, or other descriptive
    passages that require nuance, interpretation, or natural language expression. It is designed to support
    a storytelling layer in visual outputs where rich context or qualitative analysis is essential.

    RichText cards are commonly used for:
    - Executive summaries
    - Analytical commentary
    - Interpretive conclusions
    - Descriptions of trends or anomalies that span multiple data points

    The 'content' field can support markdown formatting for basic styling and emphasis. The visual rendering
    should treat this card as a standalone narrative component within a larger story structure.
    """

    kind: Literal["RichText"] = "RichText"
    title: str = Field(
        description="A short, descriptive title that summarizes the purpose or content of the rich text card."
    )
    content: str = Field(
        description=(
            "The main body of the rich text. This field is intended for natural language content "
            "that offers insight or explanation. Markdown formatting is supported and can be used "
            "to add emphasis, headings, or links if needed."
        )
    )
