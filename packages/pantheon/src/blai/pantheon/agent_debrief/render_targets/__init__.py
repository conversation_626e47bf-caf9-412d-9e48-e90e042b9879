from typing import Annotated, <PERSON>, List, get_args, Literal, Tuple

from pydantic import Field, BaseModel

from blai.pantheon.agent_debrief.render_targets.models.attack_timeline import TimelineCardProps
from blai.pantheon.agent_debrief.render_targets.models.kvp_list import KeyValuePairListProps
from blai.pantheon.agent_debrief.render_targets.models.pie_chart import PieChartConfig
from blai.pantheon.agent_debrief.render_targets.models.rich_text import RichText
from blai.pantheon.agent_debrief.render_targets.models.trend_chart import TrendChartConfig

VisualElement = Union[
    TimelineCardProps,
    KeyValuePairListProps,
    # PieChartConfig,
    # TrendChartConfig,
    RichText]

# Get the list of kinds
def get_kinds_from_union(union_type):
    kinds = []
    for t in get_args(union_type):
        field_info = t.model_fields.get("kind")
        if field_info:
            kind_value = get_args(field_info.annotation)
            if kind_value:
                kinds.extend(kind_value)
    return kinds

def get_context_from_union(union_type):
    docs = []
    for t in get_args(union_type):
        # Get the `kind` field's value from model_fields if defined
        kind = None
        field_info = t.model_fields.get("kind")  # For Pydantic v2
        if field_info:
            kind_args = get_args(field_info.annotation)
            if kind_args:
                kind = kind_args[0]  # Usually a Literal, so take the first value

        # Get the docstring of the class
        doc = getattr(t, "__doc__", None)
        if doc is not None:
            docs.append(f"[VisualElement.name]{kind}[/VisualElement.name]")
            docs.append(f"[VisualElement.docstring]{doc.strip()}[/VisualElement.docstring]")

    return "\n".join(docs)

VisualElementKinds = get_kinds_from_union(VisualElement)
VisualElementContext = get_context_from_union(VisualElement)


class AgentDebriefResponse(BaseModel):
    id: str
    elements: List[VisualElement]