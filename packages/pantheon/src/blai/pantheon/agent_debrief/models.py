from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field

from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.mitre_attack_event import MitreAttackEvent


class Events(BaseModel):
    events: List[MitreAttackEvent]


class ProcedureRunTaskSourceTypes(str, Enum):
    TOOL = 'TOOL'
    PLUGIN = 'PLUGIN'
    DATASTORE = 'DATASTORE'
    PUBLIC_DATASTORE = 'PUBLIC_DATASTORE'
    MEMORY = 'MEMORY'
    URL = 'URL'
    REPORT = 'report'  # Note: lowercase matches frontend
    OTHER = 'OTHER'
    CODE = 'code'


class Tool(BaseModel):
    id: str
    displayName: str
    type: str  # You can replace with a `toolType` Enum if one exists


class Source(BaseModel):
    name: str
    s3Path: str
    origin: ProcedureRunTaskSourceTypes


class ProcedureRunTask(BaseModel):
    id: str
    taskId: int
    prompt: str
    description: str
    outcome: str
    status: str
    failedReason: str
    sources: List[Source]
    tools: List[Tool]
    def _summary(self) -> List[str]:
        p = []
        p.append(f"[Description]{self.description}[/Description]")
        p.append(f"[Prompt]{self.prompt}[/Prompt]")
        p.append(f"[Outcome]{self.outcome}[/Outcome]")
        return p

    def summary(self) -> str:
        return "\n".join(self._summary())



class InputItem(BaseModel):
    name: str
    value: str


class ProcedureRunDetails(BaseModel):
    """
    Pydantic model representing the detailed results of a procedure run,
    for frontend visualization and interaction.
    """

    id: str = Field(description="Unique identifier for the procedure run.")
    date: datetime = Field(description="When the run occurred.")
    procedureName: str = Field(description="Human-readable name of the procedure.")
    customName: str = Field(description="User-defined name for the run instance.")
    procedureId: int = Field(description="Internal numeric ID of the procedure.")
    longTermMemory: bool = Field(description="Indicates whether LTM was used during the run.")
    outcome: str = Field(description="Full text output of the procedure.")
    executiveSummary: str = Field(description="High-level summary of the run's results.")
    keyFindings: str = Field(description="Highlights and insights extracted from the run.")
    actionsTaken: str = Field(description="Descriptions of actions performed (manual or automated).")
    trigger: str = Field(description="The mechanism or context that triggered this run.")
    childrenTasks: List[ProcedureRunTask] = Field(description="List of individual task runs within this procedure.")
    status: str = Field(description="UI-facing status of the run (e.g. success, failed, in progress).")
    failedReason: str = Field(description="Reason for failure, if the run failed.")
    inputs: List[InputItem] = Field(description="Input values used for the procedure run.")
    thumbsUpCount: int = Field(description="Number of positive (👍) user ratings.")
    thumbsDownCount: int = Field(description="Number of negative (👎) user ratings.")
    userRating: Optional[bool] = Field(description="User rating for this run: true (👍), false (👎), or null (no rating).")
    procedureRunRatingId: Optional[int] = Field(default=None, description="ID of the user's rating record if it exists.")

    def _summary(self) -> List[str]:
        p = []
        p.append(f"[ExecutiveSummary]{self.executiveSummary}[/ExecutiveSummary]")
        p.append(f"[Outcome]{self.outcome}[/Outcome]")
        p.append(f"[KeyFindings]{self.keyFindings}[/KeyFindings]")
        p.append(f"[TaskResults]")
        for t in self.childrenTasks:
            p.extend(t._summary())
        p.append(f"[/TaskResults]\n")
        return p

    def summary(self) -> str:
        return "\n".join(self._summary())
