from typing import Literal

from langchain_community.document_loaders import UnstructuredURLLoader
from langchain_core.messages import ToolMessage
from langchain_core.tools import Tool
from blai.pantheon.agents.configurable.tools.base import DeclarativeBaseTool


class LoadURLTool(DeclarativeBaseTool):
    kind: Literal["LoadURLTool"] = "LoadURLTool"

    def tool_output_formatter(self, output: ToolMessage) -> str:
        return output.content

    def __call__(self, url: str) -> str:
        loader = UnstructuredURLLoader(urls=[url])
        docs = [p.page_content for p in loader.load()]
        return "\n".join(docs)

    @property
    def tool(self):
        return  Tool(name=self.kind,
                     func=self.__call__,
                     description="Returns content from URL",
                     )
