from typing import Literal, Optional, List

from langchain_tavily import Tavily<PERSON>earch
from pydantic import Field, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict

from blai.pantheon.agents.configurable.tools.base import DeclarativeBaseTool


class TavilyToolSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="TAVILY_")

    API_KEY: SecretStr = Field(exclude=True)
    MAX_RESULTS: int = 2
    TOPIC: Literal["general", "news"] = "general"
    INCLUDE_DOMAINS: Optional[List[str]] = None
    EXCLUDE_DOMAINS: Optional[List[str]] = None
    INCLUDE_IMAGES: bool = False

    @property
    def tool(self):
        return TavilySearch(max_results=self.MAX_RESULTS, topic=self.TOPIC)

class TavilySearchTool(DeclarativeBaseTool):
    kind: Literal["TavilySearchTool"] = "TavilySearchTool"
    settings: Optional[TavilyToolSettings] = Field(default=None)

    @property
    def tool(self):
        if self.settings is None:
            self.settings = TavilyToolSettings()
        return self.settings.tool
