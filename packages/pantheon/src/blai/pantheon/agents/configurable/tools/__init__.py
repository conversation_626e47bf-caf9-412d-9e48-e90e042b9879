from typing import Annotated, Union

from pydantic import Field

from blai.pantheon.agents.configurable.tools.date_tool import DatetimeTool
from blai.pantheon.agents.configurable.tools.load_url import LoadURLTool
from blai.pantheon.agents.configurable.tools.tavily_search import TavilySearchTool

ConfigurableTool = Annotated[Union[TavilySearchTool, DatetimeTool, LoadURLTool], Field(discriminator="kind")]
