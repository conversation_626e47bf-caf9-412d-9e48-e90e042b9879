import datetime
from typing import Literal

from langchain_core.messages import ToolMessage
from langchain_core.tools import Tool

from blai.pantheon.agents.configurable.tools.base import DeclarativeBaseTool


class DatetimeTool(DeclarativeBaseTool):
    kind: Literal["DatetimeTool"] = "DatetimeTool"

    def tool_output_formatter(self, output: ToolMessage) -> str:
        return output.content

    def __call__(self, *args, **kwargs) -> str:
        """Returns the current datetime"""
        return datetime.datetime.now(datetime.UTC).isoformat()

    @property
    def tool(self):
        return  Tool(name="DatetimeTool",
                     func=self.__call__,
                     description="Returns the current datetime",
                     )
