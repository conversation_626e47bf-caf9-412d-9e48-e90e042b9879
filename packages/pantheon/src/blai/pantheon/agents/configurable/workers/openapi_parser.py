# © 2025 Bricklayer.ai. All rights reserved.
"""
OpenAPI Parser Worker - Parses and analyzes OpenAPI specifications.
"""

from blai.pantheon.agents.configurable.workers.base import Configurable<PERSON>orker
from blai.pantheon.plugin_agent.insight.techniques.openapi_analyzer import OpenAPIAnalysisResult


OpenAPIParser = ConfigurableWorker(
    name="OpenAPIParser",
    description="Parses OpenAPI specifications and extracts structured information about endpoints, security, and API structure",
    prompt=(
        "You are an OpenAPI specification analysis expert.\n\n"
        "INSTRUCTIONS:\n"
        "- Parse the provided OpenAPI specification (YAML or JSON format)\n"
        "- Extract all endpoints with their methods, paths, parameters, and responses\n"
        "- Analyze security schemes and authentication requirements\n"
        "- Categorize endpoints by functionality using tags and descriptions\n"
        "- Identify the API's complexity and provide recommendations\n"
        "- Handle malformed or non-standard specifications gracefully\n"
        "- Focus on extracting actionable information for plugin generation\n\n"
        "ANALYSIS CRITERIA:\n"
        "- Endpoint completeness (parameters, responses, descriptions)\n"
        "- Security scheme analysis\n"
        "- API structure and organization\n"
        "- Categorization by functionality\n"
        "- Complexity assessment\n\n"
        "OUTPUT FORMAT:\n"
        "Provide a structured OpenAPIAnalysisResult with:\n"
        "- Parsed specification with all endpoints\n"
        "- Analysis summary\n"
        "- Endpoint categorization\n"
        "- Security analysis\n"
        "- Complexity score (0.0-1.0)\n"
        "- Recommendations for plugin generation"
    ),
    response_format=OpenAPIAnalysisResult
)


# Future: Could add specialized parsers for different formats
PostmanParser = ConfigurableWorker(
    name="PostmanParser", 
    description="Parses Postman collection JSON and converts to OpenAPI-like structure",
    prompt=(
        "You are a Postman collection analysis expert.\n\n"
        "INSTRUCTIONS:\n"
        "- Parse the provided Postman collection JSON\n"
        "- Convert requests to OpenAPI-like endpoint structure\n"
        "- Extract authentication and environment variables\n"
        "- Organize requests by folders/collections\n"
        "- Handle Postman-specific features (pre-request scripts, tests)\n\n"
        "NOTE: This is a future enhancement - not implemented yet."
    ),
    response_format=OpenAPIAnalysisResult
)
