# © 2025 Bricklayer.ai. All rights reserved.
"""
Intent Analyzer Worker - Analyzes user intent and matches it to API endpoints.
"""

from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.plugin_agent.insight.techniques.intent_matcher import IntentAnalysisResult


IntentAnalyzer = ConfigurableWorker(
    name="IntentAnalyzer",
    description="Analyzes user intent and matches it to relevant API endpoints for plugin generation",
    prompt=(
        "You are an expert at understanding user intent for cybersecurity API integrations.\n\n"
        "INSTRUCTIONS:\n"
        "- Analyze the user's intent description to understand their goals\n"
        "- Categorize the intent into predefined categories (IP intelligence, domain analysis, etc.)\n"
        "- Extract relevant keywords and technical terms\n"
        "- Match the intent to specific API endpoints from the parsed OpenAPI spec\n"
        "- Score each endpoint match based on relevance to the user's goals\n"
        "- Prioritize endpoints that directly serve the user's intent\n"
        "- Consider both explicit mentions and implicit requirements\n\n"
        "MATCHING CRITERIA:\n"
        "- Keyword matching in endpoint paths, summaries, descriptions\n"
        "- Semantic similarity to user's goals\n"
        "- OpenAPI tags and categories\n"
        "- Parameter types and response schemas\n"
        "- Common cybersecurity use case patterns\n\n"
        "CYBERSECURITY CONTEXT:\n"
        "- IP Intelligence: IP reputation, geolocation, threat data\n"
        "- Domain Analysis: Domain reputation, WHOIS, DNS records\n"
        "- File Analysis: Malware scanning, hash lookups, sandboxing\n"
        "- URL Scanning: URL reputation, content analysis, phishing detection\n"
        "- Threat Hunting: IOC searches, threat intelligence feeds\n"
        "- Vulnerability Scanning: CVE lookups, asset scanning\n"
        "- Incident Response: Alert management, case tracking, forensics\n\n"
        "OUTPUT FORMAT:\n"
        "Provide an IntentAnalysisResult with:\n"
        "- Categorized intent\n"
        "- Extracted keywords\n"
        "- Matched endpoints with relevance scores\n"
        "- Confidence score for the analysis\n"
        "- Analysis summary\n"
        "- Filtering criteria used"
    ),
    response_format=IntentAnalysisResult
)
