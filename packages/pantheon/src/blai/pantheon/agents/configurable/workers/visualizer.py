from typing import List, Literal

from pydantic import BaseModel, Field

from blai.pantheon.agent_debrief.models import ProcedureRunDetails
from blai.pantheon.agent_debrief.render_targets import AgentDebriefResponse, VisualElement, VisualElementKinds
from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker


class VisualizerInstruction(BaseModel):
    element: str = Field(..., description=f"The element to visualize can be one of [{VisualElementKinds}]")
    instructions: str = Field(..., description="Instructions for which visual element to extract from the procedure run details")

class VisualizerRequest(BaseModel):
    instruction: VisualizerInstruction = Field(..., description="Instructions for which visual element to extract from the procedure run details")
    procedure_run_details: ProcedureRunDetails

class VisualizerResponse(BaseModel):
    result: VisualElement = Field(..., description=f"The element record to visualize.")


Visualizer = ConfigurableWorker(name="Visualizer",
                             description="Visualizer will extract a *SINGLE* visual element from a procedure run details and a request.",
                             prompt=(
                                 "You are a visualizer agent.\n\n"
                                 "INSTRUCTIONS:\n"
                                 "- Follow the instructions to extract the requested visual element from the procedure run details record.\n"
                                 "- Respond ONLY with the results of your work in the VisualElement format, do NOT include ANY other text.\n"
                             ),
                             response_format=VisualizerResponse,
                             )