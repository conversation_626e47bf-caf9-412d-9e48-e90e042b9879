# © 2025 Bricklayer.ai. All rights reserved.
from typing import Annotated, List, Optional, Type, Union

from blai.pantheon.agents.configurable.tools import ConfigurableTool
from blai.pantheon.agents.utils.json_schema.model import JSONSchemaModel
from blai.pantheon.agents.utils.json_schema.parser import parse_schema_model
from blai.pantheon.settings import AI_SETTINGS, AzureOpenAISettings
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, BeforeValidator, Field, field_serializer
from pydantic_settings import BaseSettings


class AgentSettings(BaseSettings):
    ai: AI_SETTINGS = Field(default_factory=AzureOpenAISettings)


def validate_output_model(value):
    if value is None:
        return None
    return parse_schema_model(value)


class ConfigurableWorker(BaseModel):
    """
    Represents a configurable agent worker that can be used in planning, execution, or review steps. Supports custom prompts, settings, tools, and input/output formats.
    """

    name: str
    description: str
    prompt: str
    settings: Optional[AgentSettings] = Field(default=None)
    tools: List[ConfigurableTool] = []
    request_format: Annotated[
        Optional[Union[Type[BaseModel], JSONSchemaModel, dict, str]],
        BeforeValidator(validate_output_model),
    ] = None
    response_format: Annotated[
        Union[Type[BaseModel], JSONSchemaModel, dict, str],
        BeforeValidator(validate_output_model),
    ]

    @field_serializer("request_format", when_used="json")
    def serialize_request_format(self, value, _info):
        """
        Serialize the request_format field to JSON schema if present, otherwise return None.
        """
        if value is None:
            return None
        return value.model_json_schema()

    @field_serializer("response_format", when_used="json")
    def serialize_response_format(self, value, _info):
        """
        Serialize the response_format field to JSON schema if present, otherwise return None.
        """
        if value is None:
            return None
        return value.model_json_schema()

    @property
    def full_description(self):
        """
        Return a full description of the worker, including expected input and output formats if available.
        """
        resp = f"{self.description}\n"
        if self.request_format:
            resp += (
                "[Expected Input Format]\n"
                f"{self.request_format.model_json_schema()}\n"
                "[/Expected Input Format]\n"
            )
        if self.response_format:
            resp += (
                "[Expected Output Format]\n"
                f"{self.response_format.model_json_schema()}\n"
                "[/Expected Output Format]\n"
            )
        return resp

    @property
    def full_prompt(self) -> str:
        """
        Return the full prompt for the agent, including instructions for returning structured data if a response format is specified.
        """
        if self.response_format:
            return (
                f"{self.prompt}"
                "- You are an agent tasked with returning structured data.\n"
            )
        return self.prompt

    @property
    def langchain_tools(self):
        """
        Return a list of tool callables for use with LangChain agents.
        """
        return [tool.tool for tool in self.tools]

    def create(self):
        """
        Create and return a react agent using the current settings, tools, prompt, and response format.
        """
        if self.settings:
            settings = self.settings
        else:
            settings = AgentSettings()
        return create_react_agent(
            model=settings.ai.model,
            tools=self.langchain_tools,
            prompt=self.full_prompt,
            response_format=self.response_format,
            name=self.name,
        )
