# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Generator Worker - Generates Bricklayer plugin specifications from OpenAPI YAML and user intent.
"""

from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.plugin_agent.models import ApiPluginSpec


PluginGenerator = ConfigurableWorker(
    name="PluginGenerator",
    description="Generates Bricklayer plugin specifications from OpenAPI YAML specs and user intent",
    prompt=(
        "You are an expert at generating Bricklayer plugin specifications from OpenAPI YAML specifications.\n\n"
        
        "INSTRUCTIONS:\n"
        "- Analyze the provided OpenAPI YAML specification\n"
        "- Understand the user's intent and goals\n"
        "- Select only the endpoints that are relevant to the user's intent\n"
        "- Generate a complete Bricklayer plugin specification in V2 format\n"
        "- Focus on API key authentication (prioritize over other auth methods)\n"
        "- Ensure all required fields are properly filled\n\n"
        
        "USER INTENT MATCHING:\n"
        "- Read the user's intent carefully to understand their goals\n"
        "- Look for keywords that match endpoint paths, descriptions, or functionality\n"
        "- Prioritize endpoints that directly serve the user's stated purpose\n"
        "- Include supporting endpoints if they're necessary for the main functionality\n"
        "- Exclude endpoints that don't relate to the user's intent\n\n"
        
        "OPENAPI YAML ANALYSIS:\n"
        "- Extract the base URL from the 'servers' section\n"
        "- Analyze each endpoint in the 'paths' section\n"
        "- Map OpenAPI parameters to Bricklayer Parameter format\n"
        "- Convert OpenAPI parameter types to supported types: str, int, bool, float, list, dict, file, object\n"
        "- Map parameter locations: query, header, body, path, json\n"
        "- Extract endpoint descriptions and summaries\n\n"
        
        "AUTHENTICATION HANDLING:\n"
        "- Look for API key authentication in the OpenAPI security schemes\n"
        "- Set auth_type to 'api_key' for API key authentication\n"
        "- Configure credentials with type 'api_key' and appropriate header name\n"
        "- Set auth_required=true for endpoints that need authentication\n"
        "- Set auth_location to 'header' (preferred) or 'query'\n"
        "- Set auth_header_name to the appropriate header (e.g., 'X-API-Key', 'Authorization')\n\n"
        
        "BRICKLAYER V2 FORMAT REQUIREMENTS:\n"
        "- Use PluginV2 format in the 'spec' field\n"
        "- Set base_url from OpenAPI servers\n"
        "- Create Endpoint objects for each relevant endpoint\n"
        "- Map HTTP methods correctly (GET, POST, PUT, DELETE, etc.)\n"
        "- Set content_type appropriately (default: application/json)\n"
        "- Create Parameter objects with correct types and locations\n\n"
        
        "QUALITY GUIDELINES:\n"
        "- Use clear, descriptive names and descriptions\n"
        "- Ensure parameter types match the expected data\n"
        "- Set required=true for mandatory parameters\n"
        "- Include helpful descriptions for endpoints and parameters\n"
        "- Focus on cybersecurity use cases and terminology\n\n"
        
        "OUTPUT FORMAT:\n"
        "Generate a complete ApiPluginSpec with:\n"
        "- name: Clear plugin name based on the API and user intent\n"
        "- description: Helpful description for LLM selection\n"
        "- credentials: API key configuration\n"
        "- spec: PluginV2 with base_url and relevant endpoints\n"
        "- auth_type: Set to 'api_key' for API key authentication"
    ),
    response_format=ApiPluginSpec
)
