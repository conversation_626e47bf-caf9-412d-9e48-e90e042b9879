from pydantic import BaseModel

from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker

class Report(BaseModel):
    title: str
    description: str
    body: str

ReportWriter = ConfigurableWorker(name="ReportWriter",
                                 description="Report Writer agent can write reports in markdown.",
                                 prompt=(
                                     "You are a report writing agent.\n\n"
                                     "INSTRUCTIONS:\n"
                                     "- Assist ONLY with report writing-related tasks.\n"
                                     "- Write in markdown.\n"
                                     "- Respond ONLY with the results of your work, do NOT include ANY other text."
                                 ),
                                  response_format=Report
                                 )
