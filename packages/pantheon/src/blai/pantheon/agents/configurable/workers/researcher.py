from typing import Optional, List, Literal

from pydantic import BaseModel, Field

from blai.pantheon.agents.configurable.workers.base import Configurable<PERSON>orker
from blai.pantheon.agents.configurable.tools import DatetimeTool, TavilySearchTool
from blai.pantheon.agents.configurable.tools.load_url import LoadURLTool


class ResearchSource(BaseModel):
    url: str = Field(..., description="The URL of the source.")
    summary: str = Field(..., description="A concise summary of the content found at the source.")
    content: str = Field(..., description="The content of the source found at the source.")

class ResearchResponse(BaseModel):
    summary: str = Field(..., description="A synthesized summary of the research findings across all sources.")
    sources: List[ResearchSource] = Field(..., description="A list of sources consulted, including summaries and metadata.")


class ResearchRequest(BaseModel):
    query: str
    time_range: Optional[Literal["day", "week", "month", "year"]] = Field(default=None, description="Only use these values.  Just use a range greater than the one you need then filter the results.")


Researcher = ConfigurableWorker(name="Researcher",
                                description="Researcher agent that has access to web search.",
                                request_format=ResearchRequest,
                                response_format=ResearchResponse,
                                prompt=(
                                   "You are a research agent.\n\n"
                                   "INSTRUCTIONS:\n"
                                   "- Assist ONLY with research-related tasks.\n"
                                   "- Determine the current date using the DatetimeTool first and use that in using TavilySearchTool.\n"
                                   "- Then you *MUST* search using TavilySearchTool.\n"
                                   "- After you're done with your tasks, respond to the supervisor directly\n"
                                   "- Respond ONLY with a ResearchResponse record, do NOT include ANY other text."
                               ),
                                tools=[DatetimeTool(), TavilySearchTool(), LoadURLTool()])
