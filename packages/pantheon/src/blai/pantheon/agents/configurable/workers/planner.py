from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel, Field

from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker


class AgentDescription(BaseModel):
    name: str = Field(None, description="Name of the agent.")
    description: str = Field(None, description="Description of the agent.")

class PlanStepResult(BaseModel):
    step_id: int = Field(..., description="The order in which the step should be executed, starting from 1.")
    agent: str = Field(..., description="The name of the agent or tool responsible for this step.")
    prompt: str = Field(..., description="The instruction or input to be passed to the agent for execution.")
    step_outcome: Dict[str, Any] = Field(..., description="Outcome of the step.")


class Review(BaseModel):
    replan: bool
    reason: str


class PlanRequest(BaseModel):
    user_prompt: str
    agents: List[AgentDescription]
    review: Optional[Review] = None
    results: List[PlanStepResult] = Field(default_factory=list, description="The results of the plan.")
    def summary(self) -> str:
        return (f"[User Prompt]\n{self.user_prompt}\n\n" 
                f"[Agents]\n{[agent.model_dump(mode='json') for agent in self.agents]}\n\n" 
                f"[Results]\n{[result.model_dump(mode='json') for result in self.results]}\n\n"
                f"[Review]\n{self.review.model_dump(mode='json') if self.review else None}")


class PlanStep(BaseModel):
    """
    Represents a single step in a multi-step plan generated by a planner agent.

    Each step specifies:
    - The agent or tool responsible for executing the step.
    - A prompt or instruction to be passed to that agent.
    - The step's position in the execution order (step_id).
    """
    step_id: int = Field(..., description="The order in which the step should be executed, starting from 1.")
    agent: str = Field(..., description="The name of the agent or tool responsible for this step.")
    prompt: str = Field(..., description="The instruction or input to be passed to the agent for execution.")
    context: str = Field(..., description="Context necessary to execute the step.")
    depends_on: List[int] = Field(default_factory=list, description="List of step_ids whose outputs are required to be added to the context of this step.")


class Plan(BaseModel):
    """
    A structured plan consisting of a series of ordered steps created by a planner agent.

    The plan includes:
    - An overall objective describing the user's intent or goal.
    - A list of individual steps, each defining who does the work and with what prompt.

    This format is suitable for LLM-driven planning and workflow orchestration.
    """
    objective: str = Field(..., description="The high-level goal or intent that the plan aims to achieve.")
    steps: List[PlanStep] = Field(..., description="An ordered list of steps to achieve the objective.")


