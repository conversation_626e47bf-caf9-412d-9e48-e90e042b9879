from typing import List

from pydantic import BaseModel, Field

from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.mitre_attack_event import MitreAttackEvent
from blai.pantheon.agent_debrief.render_targets import AgentDebriefResponse
from blai.pantheon.agents.configurable.supervisors.base import ConfigurableSupervisor, Review
from blai.pantheon.agents.configurable.workers.base import Configurable<PERSON>orker
from blai.pantheon.agents.configurable.workers.planner import PlanRequest, Plan
from blai.pantheon.agents.configurable.workers.visualizer import Visualizer, VisualizerInstruction

DebriefReviewer = ConfigurableWorker(
    name="DebriefReviewer",
    description="Checks if the generated plan steps yielded all required components",
    prompt=(
        "You are a reviewer checking the results of a debrief plan execution.\n\n"
        "REVIEW CRITERIA:\n"
        "- Ensure the outputs are coherent and complete relative to the original user prompt.\n\n"
        "- DO NOT REJECT REDUNDANT ENTRIES.  They can be removed by Summarizer, which only happens if replan is false"
        "Your job:\n"
        "- If any required element is missing, respond with {'replan': true, 'reason': '<why>'}.\n"
        "- Otherwise, respond with {'replan': false, 'reason': 'All criteria met'}."
    ),
    response_format=Review,
)

DebriefPlanner = ConfigurableWorker(name="DebriefPlanner",
                             description="DebriefPlanner provides a plan given a list of agents and a request",
                             prompt=(
                                 "You are a plan writing agent.\n\n"
                                 "INSTRUCTIONS:\n"
                                 "- Given a list of agents and a request make a plan for it to be accomplished.\n"
                                 "- Only use the list of Agents from the PlanRequest to make a plan.\n"
                                 f"- PlanStep prompt *MUST* be in the format of {VisualizerInstruction.model_json_schema()}\n"
                                 "- The depends_on in PlanStep should only be used if the results of a predecessor are required, otherwise leave as an empty list.\n"
                                 "- Minimum Required VisualElements for the Visualizer: 1. Executive Summary (RichTextCard **MUST BE FIRST**), 2. Key Findings (KeyValuePair or Table)\n"
                                 "- If events with dates and/or times are present then provide a TimelineCard.\n"
                                 "- Additionally, you must cover all information present in the ProcedureRunDetails.\n"
                             ),
                             request_format=PlanRequest,
                             response_format=Plan,
                             )

class MitreAttackEventList(BaseModel):
    events: List[MitreAttackEvent] = Field(..., description="List of extracted MITRE ATT&CK events")

MitreAttackAnalyzer = ConfigurableWorker(
    name="MitreAttackAnalyzer",
    description="Analyzes input text and extracts zero or more MITRE ATT&CK events.",
    prompt=(
        "You are an incident analysis agent.\n\n"
        "INSTRUCTIONS:\n"
        "- Read the provided input and extract all relevant events aligned with the MITRE ATT&CK framework.\n"
        "- Each event must include a technique ID, name, tactic, target system, and timestamp if available.\n"
        "- Include IOCs, process execution data, and actor information where available.\n"
        "- Return your output strictly as a list of structured events using the MitreAttackEventList format.\n"
        "- If no MITRE-relevant activity is present, return an empty list."
    ),
    response_format=MitreAttackEventList
)
DebriefPlanner = ConfigurableWorker(name="DebriefPlanner",
                                    description="DebriefPlanner provides a plan given a list of agents and a request",
                                    prompt=(
                                        "You are a plan writing agent.\n\n"
                                        "INSTRUCTIONS:\n"
                                        "- Given a list of agents and a request, make a multi-step plan.\n"
                                        "- You MUST use all appropriate agents from the PlanRequest.\n"
                                        "- Agents such as MitreAttackAnalyzer should be used to extract structured data from unstructured input (e.g., logs or debriefs).\n"
                                        "- Do NOT skip intermediate analytical agents.\n"
                                        f"- PlanStep.prompt attribute *MUST* be in the format of {VisualizerInstruction.model_json_schema()}\n"
                                        "- The depends_on field must reference IDs of prior steps if their output is needed.\n"
                                        "- Cover all information from ProcedureRunDetails.\n"
                                    ),
                                    request_format=PlanRequest,
                                    response_format=Plan,
                                    )

AgentDebrief = ConfigurableSupervisor(
    name="AgentDebrief",
    prompt='',
    planner=DebriefPlanner,
    response_format=AgentDebriefResponse,
    workers=[MitreAttackAnalyzer, Visualizer],  # include other workers like Visualizer, Analyst, etc.
)
