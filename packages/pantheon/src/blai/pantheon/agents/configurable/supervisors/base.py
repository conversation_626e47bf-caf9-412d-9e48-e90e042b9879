import copy
from typing import List, Dict, Any, Optional, Type
import json

from langchain_core.messages import HumanMessage
from langchain_core.runnables import Runna<PERSON>, RunnableL<PERSON>b<PERSON>
from langgraph.constants import END
from langgraph.graph import StateGraph
from pydantic import BaseModel

from blai.pantheon.agent_debrief.models import ProcedureRunDetails
from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.agents.configurable.workers.planner import Plan, AgentDescription, PlanRequest, PlanStep, \
    PlanStepResult, Review


class ExecutionState(BaseModel):
    user_prompt: str
    plan: Optional[Plan] = None
    results: List[PlanStepResult] = []
    review: Optional[Review] = None
    structured_response: Optional[Dict[str, Any]] = None

class ConfigurableSupervisor(ConfigurableWorker):
    planner: ConfigurableWorker
    reviewer: Optional[ConfigurableWorker] = None
    response_format: Type[BaseModel]
    workers: List[ConfigurableWorker] = []
    prompt: str = ""
    description: str = ""

    def _executor(self) -> Runnable:
        def execute_plan(state: ExecutionState) -> ExecutionState:
            results = []
            agent_lookup = {agent.name: agent.create() for agent in self.workers}
            step_lookup = sorted(state.plan.steps, key=lambda s: s.step_id)

            for step in step_lookup:
                agent_name = step.agent

                if agent_name not in agent_lookup:
                    raise ValueError(f"Agent '{agent_name}' not found.")

                agent_inputs = [
                    f"[Prompt]{step.prompt}[/Prompt]",
                    f"[System Context]{step.context}[/System Context]",
                    f"[User Prompt]{state.user_prompt}[/User Prompt]\n"
                ]

                result_lookup = {s.step_id: s for s in sorted(results, key=lambda s: s.step_id)}
                for dep in step.depends_on:
                    result = result_lookup[dep]
                    agent_inputs.append(f"[Step {dep} Result]{result.model_dump(mode='json')}[/Step {dep} Result]")

                agent_input = "\n".join(agent_inputs)

                msg = {"messages": [HumanMessage(content=agent_input)]}
                raw_result = agent_lookup[agent_name].invoke(msg)
                structured = raw_result["structured_response"].model_dump(mode="json")

                results.append(PlanStepResult(
                    step_id=step.step_id,
                    agent=agent_name,
                    prompt=step.prompt,
                    step_outcome=structured,
                ))

            results = results + state.results
            return ExecutionState(
                user_prompt=state.user_prompt,
                plan=state.plan,
                results=results,
                review=None
            )

        return RunnableLambda(execute_plan)

    def _reviewer_wrapper(self) -> Runnable:
        if self.settings and self.planner.settings is None:
            self.reviewer.settings = self.settings
        reviewer = self.reviewer.create()
        def review_plan(state: ExecutionState) -> ExecutionState:
            review_input = (
            f"Objective: {state.plan.objective}\n"
            f"Steps: [{[s.model_dump(mode='json') for s in state.plan.steps]}]\n"
            f"Results: {[s.model_dump(mode='json') for s in state.results]}\n"
            )
            msg = {"messages": [HumanMessage(content=review_input)]}
            review_output = reviewer.invoke(msg)["structured_response"]
            if review_output.replan:
                results = []
            else:
                results = state.results
            return ExecutionState(
                user_prompt=state.user_prompt,
                plan=state.plan,
                results=results,
                review=review_output
            )

        return RunnableLambda(review_plan)

    def _summarizer_wrapper(self) -> Runnable:
        summarizer = ConfigurableWorker(
            name="Summarizer",
            description="Summarizer converts the results into the requested format.",
            settings=self.settings,
            prompt=(
                "You will receive:\n"
                "- The original objective.\n"
                "- The steps that were planned and executed.\n"
                "- The results of each step.\n"
                "Your task:\n"
                f"- Structure the inputs into the {self.response_format.__name__} format.\n"
            ),
            response_format=self.response_format).create()

        def summarize(state: ExecutionState) -> ExecutionState:
            summarizer_input = (
                f"Objective: {state.plan.objective}\n"
                f"Steps: [{[s.model_dump(mode='json') for s in state.plan.steps]}]\n"
                f"Results: {[s.model_dump(mode='json') for s in state.results]}\n"
                f"Review: {state.review}"
            )
            msg = {"messages": [HumanMessage(content=summarizer_input)]}
            resp: BaseModel = summarizer.invoke(msg)["structured_response"]

            # Handle a situation where settings is only set at the supervisor
            return ExecutionState(
                user_prompt=state.user_prompt,
                plan=state.plan,
                results=state.results,
                review=state.review,
                structured_response=resp.model_dump(mode='json')
            )
        return RunnableLambda(summarize)

    def _planner_wrapper(self) -> Runnable:
        # Handle a situation where settings is only set at the supervisor
        if self.settings and self.planner.settings is None:
            self.planner.settings = self.settings
        planner = self.planner.create()
        def plan_from_state(state: ExecutionState) -> ExecutionState:
            planner_input = PlanRequest(
                user_prompt=state.user_prompt,
                agents=[AgentDescription(name=w.name, description=w.full_description) for w in self.workers],
                results=state.results,
                review=state.review,
            )
            plan = planner.invoke({"messages": [HumanMessage(content=planner_input.summary())]})["structured_response"]
            return ExecutionState(user_prompt=state.user_prompt, plan=plan, results=state.results, review=None)

        return RunnableLambda(plan_from_state)

    def create(self):
        planner = self._planner_wrapper()
        executor = self._executor()
        summarizer = self._summarizer_wrapper()

        builder = StateGraph(ExecutionState)

        builder.add_node("planner", planner)
        builder.add_node("executor", executor)
        builder.add_node("summarizer", summarizer)

        builder.set_entry_point("planner")
        builder.add_edge("planner", "executor")
        if self.reviewer:
            reviewer = self._reviewer_wrapper()
            builder.add_node("reviewer", reviewer)
            builder.add_edge("executor", "reviewer")

            def decision(state: ExecutionState) -> str:
                if state.review.replan:
                    return "planner"
                return "summarizer"

            builder.add_conditional_edges("reviewer", decision)
        else:
            builder.add_edge("executor", "summarizer")
        builder.add_edge("summarizer", END)

        return builder.compile()

if __name__ == "__main__":
    from blai.pantheon.agent_debrief.processor import AgentDebriefProcessor, AgentDebriefProcessingRequest
    import json

    with open("/Users/<USER>/projects/mason/packages/pantheon/src/blai/pantheon/agents/configurable/supervisors/rep.json", "r") as f:
        rep = ProcedureRunDetails(**json.load(f))


    resp = AgentDebriefProcessor()(AgentDebriefProcessingRequest(procedure_run_output=rep))

    print(resp)


