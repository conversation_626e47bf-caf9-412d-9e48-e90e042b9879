# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Agent Supervisor - Orchestrates plugin generation workflow.
"""

from blai.pantheon.agents.configurable.supervisors.base import ConfigurableSupervisor
from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.agents.configurable.workers.planner import Plan, PlanRequest
from blai.pantheon.agents.configurable.workers.plugin_generator import PluginGenerator
from blai.pantheon.plugin_agent.render_targets import PluginAgentResponse


PluginPlanner = ConfigurableWorker(
    name="PluginPlanner",
    description="Creates execution plans for plugin generation workflow",
    prompt=(
        "You are a plugin generation workflow planner.\n\n"
        "INSTRUCTIONS:\n"
        "- Create a simple plan for generating a Bricklayer plugin\n"
        "- Use the PluginGenerator to analyze the OpenAPI YAML and user intent\n"
        "- Keep the workflow simple and focused\n\n"
        
        "WORKFLOW:\n"
        "1. PluginGenerator: Analyze OpenAPI YAML and user intent to generate Bricklayer plugin specification\n\n"
        
        "CONTEXT REQUIREMENTS:\n"
        "- Include the full user request (OpenAPI YAML content + user intent)\n"
        "- Ensure the PluginGenerator has all necessary information\n\n"
        
        "Keep the plan simple and direct - we just need to generate the plugin specification."
    ),
    request_format=PlanRequest,
    response_format=Plan,
)


PluginAgent = ConfigurableSupervisor(
    name="PluginAgent",
    prompt="",
    planner=PluginPlanner,
    response_format=PluginAgentResponse,
    workers=[
        PluginGenerator
    ]
)
