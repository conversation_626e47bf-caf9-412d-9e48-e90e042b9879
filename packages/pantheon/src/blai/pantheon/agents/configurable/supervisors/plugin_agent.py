# © 2025 Bricklayer.ai. All rights reserved.
"""
Plugin Agent Supervisor - Orchestrates plugin generation workflow.
"""

from blai.pantheon.agents.configurable.supervisors.base import ConfigurableSupervisor, Review
from blai.pantheon.agents.configurable.workers.base import Configurable<PERSON>orker
from blai.pantheon.agents.configurable.workers.planner import Plan, PlanRequest
from blai.pantheon.agents.configurable.workers.openapi_parser import OpenAPIParser
from blai.pantheon.agents.configurable.workers.intent_analyzer import IntentAnalyzer
from blai.pantheon.agents.configurable.workers.plugin_generator import PluginGenerator
from blai.pantheon.agents.configurable.workers.plugin_validator import PluginValidator
from blai.pantheon.plugin_agent.render_targets import PluginAgentResponse


PluginReviewer = ConfigurableWorker(
    name="PluginReviewer",
    description="Reviews the plugin generation workflow results to ensure quality and completeness",
    prompt=(
        "You are a plugin generation quality reviewer.\n\n"
        "REVIEW CRITERIA:\n"
        "- Ensure all workflow steps completed successfully\n"
        "- Verify the generated plugin meets user intent\n"
        "- Check that validation passed or issues are acceptable\n"
        "- Confirm the plugin is ready for production use\n"
        "- Assess overall quality and usability\n\n"
        "QUALITY STANDARDS:\n"
        "- Plugin specification is complete and valid\n"
        "- User intent is properly addressed\n"
        "- Endpoint selection is appropriate\n"
        "- Authentication is correctly configured\n"
        "- No critical validation errors\n"
        "- Documentation is clear and helpful\n\n"
        "DECISION CRITERIA:\n"
        "- If critical issues exist or user intent is not met: {'replan': true, 'reason': '<specific issues>'}\n"
        "- If plugin quality is acceptable: {'replan': false, 'reason': 'Plugin generation successful'}\n\n"
        "Your job:\n"
        "- Review all workflow results comprehensively\n"
        "- Focus on user satisfaction and plugin quality\n"
        "- Be strict about critical issues but flexible on minor ones\n"
        "- Provide clear reasoning for your decision"
    ),
    response_format=Review,
)


PluginPlanner = ConfigurableWorker(
    name="PluginPlanner",
    description="Creates execution plans for plugin generation workflow",
    prompt=(
        "You are a plugin generation workflow planner.\n\n"
        "INSTRUCTIONS:\n"
        "- Create a comprehensive plan for generating a Bricklayer plugin\n"
        "- Use all available agents in the correct sequence\n"
        "- Ensure proper dependencies between steps\n"
        "- Include validation and quality checks\n"
        "- Handle different input formats appropriately\n\n"
        "WORKFLOW SEQUENCE:\n"
        "1. OpenAPIParser: Parse and analyze the uploaded specification\n"
        "2. IntentAnalyzer: Analyze user intent and match to endpoints\n"
        "3. PluginGenerator: Generate the Bricklayer plugin specification\n"
        "4. PluginValidator: Validate the generated plugin\n\n"
        "STEP DEPENDENCIES:\n"
        "- IntentAnalyzer depends on OpenAPIParser results\n"
        "- PluginGenerator depends on both OpenAPIParser and IntentAnalyzer\n"
        "- PluginValidator depends on PluginGenerator output\n\n"
        "CONTEXT REQUIREMENTS:\n"
        "- Include the full user request (OpenAPI spec + intent)\n"
        "- Pass relevant results between dependent steps\n"
        "- Ensure each step has sufficient context to succeed\n\n"
        "QUALITY CONSIDERATIONS:\n"
        "- Plan for iterative refinement if needed\n"
        "- Include validation at each critical step\n"
        "- Ensure comprehensive coverage of user requirements"
    ),
    request_format=PlanRequest,
    response_format=Plan,
)


PluginAgent = ConfigurableSupervisor(
    name="PluginAgent",
    prompt="",
    planner=PluginPlanner,
    reviewer=PluginReviewer,
    response_format=PluginAgentResponse,
    workers=[
        OpenAPIParser,
        IntentAnalyzer, 
        PluginGenerator,
        PluginValidator
    ]
)
