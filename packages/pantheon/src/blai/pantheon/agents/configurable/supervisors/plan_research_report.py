from pydantic import BaseModel

from blai.pantheon.agents.configurable.supervisors.base import ConfigurableSupervisor
from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.agents.configurable.workers.planner import PlanRequest, Plan, Review
from blai.pantheon.agents.configurable.workers.report_writer import ReportWriter
from blai.pantheon.agents.configurable.workers.researcher import Researcher

Planner = ConfigurableWorker(name="Planner",
                             description="Planner provides a plan given a list of agents and a request",
                             prompt=(
                                 "You are a plan writing agent.\n\n"
                                 "INSTRUCTIONS:\n"
                                 "- Given a list of agents and a request make a plan for it to be accomplished.\n"
                                 "- Only use the list of Agents from the PlanRequest to make a plan.\n"
                                 "- Context should include ALL information necessary for the task from the User Prompt.\n"
                                 "- The depends_on in PlanStep should only be used if the results of a predecessor are required, otherwise leave as an empty list.\n"
                             ),
                             request_format=PlanRequest,
                             response_format=Plan,
                             )

Reviewer = ConfigurableWorker(
    name="Reviewer",
    description="Checks if the generated plan steps yielded all required components",
    prompt=(
        "You are a reviewer checking the results of a Research Plan and Write.\n\n"
        "REVIEW CRITERIA:\n"
        "- Ensure the outputs are coherent and complete relative to the original user prompt.\n\n"
        "- DO NOT REJECT REDUNDANT ENTRIES.  They can be removed by Summarizer, which only happens if replan is false"
        "Your job:\n"
        "- If any required element is missing, respond with {'replan': true, 'reason': '<why>'}.\n"
        "- Otherwise, respond with {'replan': false, 'reason': 'All criteria met'}."
    ),
    response_format=Review,
)

class Result(BaseModel):
    content: str


PlanResearchWrite = ConfigurableSupervisor(name="PlanResearchWrite",
                                           prompt="",
                                           planner=Planner,
                                           reviewer=Reviewer,
                                           response_format=Result,
                                           workers=[Researcher, ReportWriter])
