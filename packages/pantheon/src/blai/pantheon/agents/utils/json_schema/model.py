from typing import List, Optional, Literal
from pydantic import BaseModel, field_validator


class JSONSchemaProperty(BaseModel):
    type: Optional[str]  # loosen type validation to allow any string for testing
    items: Optional["JSONSchemaProperty"] = None
    # other fields...

    @field_validator("type")
    def validate_type(cls, v):
        allowed = {"string", "integer", "number", "boolean", "array", "object"}
        if v is not None and v not in allowed:
            # for testing fallback, don't raise but mark as unknown
            # or raise depending on your needs
            return v
        return v


class JSONSchemaModel(BaseModel):
    title: str = "DynamicOutputModel"
    type: Literal["object"] = "object"
    properties: dict[str, JSONSchemaProperty]
    required: Optional[List[str]] = []

    # Needed for forward references (recursive 'items')
    model_config = {"arbitrary_types_allowed": True}


JSONSchemaProperty.model_rebuild()
