import inspect
import json
from typing import Any, Type
from pydantic import create_model, BaseModel
from blai.pantheon.agents.utils.json_schema.model import JSONSchemaModel, JSONSchemaProperty

def parse_schema_model(schema_model: JSONSchemaModel) -> type:
    if isinstance(schema_model, dict):
        schema_model = JSONSchemaModel(**schema_model)
    if not isinstance(schema_model, JSONSchemaModel) and inspect.isclass(schema_model):
        return schema_model
    missing_fields = set(schema_model.required or []) - set(schema_model.properties.keys())
    if missing_fields:
        raise ValueError(f"Required fields missing from properties: {missing_fields}")

    fields = {
        name: (_map_property_type(prop), ... if name in schema_model.required else None)
        for name, prop in schema_model.properties.items()
    }

    return create_model(schema_model.title, **fields)

# In parser.py
def _map_property_type(prop: JSONSchemaProperty | None) -> type:
    if prop is None:
        return Any  # fallback if items missing
    match prop.type:
        case "string":
            return str
        case "integer":
            return int
        case "number":
            return float
        case "boolean":
            return bool
        case "array":
            return list[_map_property_type(prop.items)]
        case "object":
            return dict
        case _:
            return Any

SHORTEN_KEY_MAP = {
    "title": "ti",
    "type": "t",
    "properties": "p",
    "required": "r",
    "items": "i",
    "description": "d",
    "enum": "e"
}

def shorten_keys(obj, key_map=None):
    if key_map is None:
        key_map = SHORTEN_KEY_MAP
    if isinstance(obj, dict):
        return {key_map.get(k, k): shorten_keys(v, key_map) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [shorten_keys(item, key_map) for item in obj]
    return obj

def compact_schema_from_pydantic(model: type[BaseModel]) -> str:
    schema_dict = model.model_json_schema()
    compacted = shorten_keys(schema_dict)
    return json.dumps(compacted, separators=(',', ':'))
