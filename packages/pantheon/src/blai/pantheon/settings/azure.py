from typing import Literal

from langchain_openai import AzureChatOpenAI
from pydantic import SecretStr, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from langchain_core.rate_limiters import InMemoryRateLimiter

S0 = InMemoryRateLimiter(
    requests_per_second=500,  # 500 tokens per second allowed
    check_every_n_seconds=0.1,  # check every 100ms
    max_bucket_size=1000  # allow bursts of up to 1000 tokens
)



class AzureOpenAISettings(BaseSettings):
    kind: Literal["AzureOpenAISettings"] = "AzureOpenAISettings"
    model_config = SettingsConfigDict(
        env_prefix="AZURE_OPENAI_",
        env={"API_VERSION": "OPENAI_API_VERSION"}
    )

    DEPLOYMENT: str = "gpt-4o-mini"
    ENDPOINT: str = "https://local-bricklayerai.openai.azure.com/"
    API_KEY: SecretStr = Field(exclude=True)
    API_VERSION: str = "2025-01-01-preview"

    @property
    def model(self):


        model = AzureChatOpenAI(
            azure_deployment=self.DEPLOYMENT,
            azure_endpoint=self.ENDPOINT,
            openai_api_version=self.API_VERSION,
            openai_api_key=self.API_KEY.get_secret_value(),
            model_version=self.API_VERSION,
            rate_limiter=S0,
        )
        return model
