import importlib
import pkgutil
import pytest

def import_submodules(package_name):
    """
    Recursively import all submodules and subpackages under the given package.
    """
    try:
        package = importlib.import_module(package_name)
    except Exception as e:
        pytest.fail(f"Failed to import root package '{package_name}': {e}")

    yield package_name

    if hasattr(package, '__path__'):  # package, not just a module
        for finder, name, ispkg in pkgutil.walk_packages(package.__path__, package_name + '.'):
            try:
                importlib.import_module(name)
            except Exception as e:
                pytest.fail(f"Failed to import module '{name}': {e}")
            yield name

def test_import_all_blai_pantheon_modules():
    """
    Test that imports all submodules under 'blai.pantheon' recursively.
    """
    # This will trigger pytest.fail if any import fails
    list(import_submodules("blai.pantheon"))
