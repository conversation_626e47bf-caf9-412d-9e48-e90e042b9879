import pytest
import json
from pydantic import BaseModel

from blai.pantheon.agents.utils.json_schema.model import JSONSchemaModel, JSONSchemaProperty
from blai.pantheon.agents.utils.json_schema.parser import (
    parse_schema_model,
    shorten_keys,
    compact_schema_from_pydantic,
)


def test_parse_json_schema_from_model():
    schema = JSONSchemaModel(
        title="ResearchOutput",
        properties={
            "summary": JSONSchemaProperty(type="string"),
            "score": JSONSchemaProperty(type="number")
        },
        required=["summary", "score"]
    )

    model_cls = parse_schema_model(schema)
    instance = model_cls(summary="Test", score=1.23)
    assert instance.summary == "Test"
    assert instance.score == 1.23


def test_parse_json_schema_from_dict():
    schema_dict = {
        "title": "DictBasedModel",
        "type": "object",
        "properties": {
            "description": {"type": "string"},
            "rating": {"type": "number"}
        },
        "required": ["description"]
    }

    model_cls = parse_schema_model(schema_dict)
    instance = model_cls(description="It works!", rating=4.5)
    assert instance.description == "It works!"
    assert instance.rating == 4.5


def test_parse_json_schema_from_json_string():
    schema_json = json.dumps({
        "title": "JSONStringModel",
        "type": "object",
        "properties": {
            "tag": {"type": "string"},
            "count": {"type": "integer"}
        },
        "required": ["tag"]
    })

    schema_dict = json.loads(schema_json)
    schema_model = JSONSchemaModel(**schema_dict)
    model_cls = parse_schema_model(schema_model)

    instance = model_cls(tag="example", count=42)
    assert instance.tag == "example"
    assert instance.count == 42


def test_missing_required_field_raises():
    schema = JSONSchemaModel(
        title="MissingFieldModel",
        properties={
            "title": JSONSchemaProperty(type="string"),
            "views": JSONSchemaProperty(type="integer"),
        },
        required=["title"]
    )
    model_cls = parse_schema_model(schema)

    with pytest.raises(Exception) as exc_info:
        model_cls(views=100)  # missing required "title"

    assert "title" in str(exc_info.value)


def test_wrong_type_for_field_raises():
    schema = JSONSchemaModel(
        title="WrongTypeModel",
        properties={
            "score": JSONSchemaProperty(type="number"),
        },
        required=["score"]
    )
    model_cls = parse_schema_model(schema)

    with pytest.raises(Exception) as exc_info:
        model_cls(score="high")  # "high" is not a float

    assert "score" in str(exc_info.value)


def test_array_with_wrong_item_type_raises():
    schema = JSONSchemaModel(
        title="ArrayTypeModel",
        properties={
            "tags": JSONSchemaProperty(type="array", items=JSONSchemaProperty(type="string")),
        },
        required=["tags"]
    )
    model_cls = parse_schema_model(schema)

    with pytest.raises(Exception) as exc_info:
        model_cls(tags=[123, "valid"])  # 123 is not a string

    assert "tags" in str(exc_info.value)


def test_missing_property_definition_raises():
    schema = JSONSchemaModel(
        title="EmptyPropertiesModel",
        properties={},  # no defined properties
        required=["foo"]
    )

    with pytest.raises(ValueError) as exc_info:
        parse_schema_model(schema)

    assert "foo" in str(exc_info.value)


# 🔥 Additional tests to hit uncovered branches

def test_parse_schema_model_accepts_dict_input():
    schema_dict = {
        "title": "FromDict",
        "type": "object",
        "properties": {
            "name": {"type": "string"}
        },
        "required": ["name"]
    }

    model_cls = parse_schema_model(schema_dict)
    instance = model_cls(name="test")
    assert instance.name == "test"


def test_unknown_property_type_defaults_to_any():
    schema = JSONSchemaModel(
        title="ArrayAnyType",
        properties={
            "data": JSONSchemaProperty(
                type="array",
                items=JSONSchemaProperty(type="mystery")  # unknown item type
            )
        },
        required=["data"]
    )
    model_cls = parse_schema_model(schema)

    # Pass a list to satisfy the 'array' type
    instance = model_cls(data=[123, "string", {"foo": "bar"}])  # should accept any item type
    assert instance.data == [123, "string", {"foo": "bar"}]


def test_object_type_maps_to_dict():
    schema = JSONSchemaModel(
        title="ObjectType",
        properties={"config": JSONSchemaProperty(type="object")},
        required=["config"]
    )
    model_cls = parse_schema_model(schema)
    instance = model_cls(config={"a": 1})
    assert instance.config["a"] == 1


def test_array_type_defaults_items_to_any():
    schema = JSONSchemaModel(
        title="ArrayAnyType",
        properties={"data": JSONSchemaProperty(type="array", items=None)},  # explicit None items
        required=["data"]
    )
    model_cls = parse_schema_model(schema)
    instance = model_cls(data=[1, "str", {}])  # should accept any item type because fallback is Any
    assert instance.data == [1, "str", {}]


def test_shorten_keys_works_on_dict():
    input_dict = {
        "title": "Example",
        "properties": {
            "foo": {
                "type": "string"
            }
        },
        "required": ["foo"]
    }

    shortened = shorten_keys(input_dict)
    assert "ti" in shortened
    assert "p" in shortened
    assert "foo" in shortened["p"]


def test_compact_schema_from_pydantic_model():
    class DummyModel(BaseModel):
        name: str
        age: int

    compact = compact_schema_from_pydantic(DummyModel)
    assert isinstance(compact, str)
    assert "ti" in compact or "t" in compact
    assert "name" in compact
