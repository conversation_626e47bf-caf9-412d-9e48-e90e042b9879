import os

import pytest
from unittest.mock import patch, MagicMock

from pydantic import BaseModel

from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.agents.configurable.tools import DatetimeTool


# Dummy input/output schemas
class DummyInput(BaseModel):
    input: str

class DummyOutput(BaseModel):
    output: str


def test_full_description_and_prompt_with_formats():
    worker = ConfigurableWorker(
        name="TestWorker",
        description="Test agent",
        prompt="Respond with structure.",
        request_format=DummyInput,
        response_format=DummyOutput,
    )

    full_desc = worker.full_description
    assert "[Expected Input Format]" in full_desc
    assert "[Expected Output Format]" in full_desc
    assert "Test agent" in full_desc

    full_prompt = worker.full_prompt
    assert "structured data" in full_prompt
    assert "Respond with structure." in full_prompt


def test_full_prompt_without_response_format():
    worker = ConfigurableWorker(
        name="TestWorker",
        description="No output format",
        prompt="Just respond.",
        response_format=DummyOutput  # still required per model
    )

    worker.__dict__["response_format"] = None
    assert worker.full_prompt == "Just respond."


def test_langchain_tools_property_with_discriminated_union():
    tool_instance = DatetimeTool()

    worker = ConfigurableWorker(
        name="ToolWorker",
        description="Tests tool union",
        prompt="Use the tool",
        tools=[tool_instance],
        response_format=DummyOutput,
    )

    tools = worker.langchain_tools
    assert len(tools) == 1
    assert callable(tools[0])


@patch("blai.pantheon.agents.configurable.workers.base.create_react_agent")
@patch.dict(os.environ, {"AZURE_OPENAI_API_KEY": "fake-api-key"})
def test_create_calls_create_react_agent(mock_create_react_agent):
    mock_create_react_agent.return_value = "agent_instance"

    worker = ConfigurableWorker(
        name="AgentWorker",
        description="For testing",
        prompt="Use structured reply",
        response_format=DummyOutput,
    )

    result = worker.create()
    assert result == "agent_instance"
    mock_create_react_agent.assert_called_once()
    kwargs = mock_create_react_agent.call_args.kwargs
    assert kwargs["name"] == "AgentWorker"
    assert kwargs["prompt"].startswith("Use structured reply")


def test_serializers():
    worker = ConfigurableWorker(
        name="SerializerTest",
        description="Serialize me",
        prompt="Check serializers",
        request_format=DummyInput,
        response_format=DummyOutput,
    )

    request_json = worker.serialize_request_format(worker.request_format, None)
    response_json = worker.serialize_response_format(worker.response_format, None)

    assert isinstance(request_json, dict)
    assert isinstance(response_json, dict)
    assert "properties" in response_json
