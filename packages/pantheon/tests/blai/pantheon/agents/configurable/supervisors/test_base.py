import os
import unittest
from unittest.mock import MagicMock, patch
from blai.pantheon.agents.configurable.supervisors.base import ConfigurableSupervisor, ExecutionState
from blai.pantheon.agents.configurable.workers.base import ConfigurableWorker
from blai.pantheon.agents.configurable.workers.planner import Plan, PlanStep, PlanStepResult, Review
from pydantic import BaseModel


class DummyResponseFormat(BaseModel):
    result: str


class TestConfigurableSupervisor(unittest.TestCase):
    @patch.dict(os.environ, {"AZURE_OPENAI_API_KEY": "fake-api-key"})
    @patch("blai.pantheon.agents.configurable.supervisors.base.ConfigurableWorker.create")
    def test_supervisor_with_mocked_summarizer(self, mock_create):
        # ---- Mock planner ----
        planner_worker = MagicMock(ConfigurableWorker)
        mock_plan = MagicMock(Plan)
        mock_plan.objective = "Objective"
        mock_plan.steps = []
        planner_worker.create.return_value.invoke.return_value = {
            "structured_response": mock_plan
        }

        # ---- Mock agent ----
        agent_worker = MagicMock(ConfigurableWorker)
        agent_worker.name = "agent"
        agent_worker.full_description = "Agent description"
        agent_worker.create.return_value.invoke.return_value = {
            "structured_response": DummyResponseFormat(result="agent result")
        }

        # ---- Mock reviewer ----
        reviewer_worker = MagicMock(ConfigurableWorker)
        reviewer_output = MagicMock(Review)
        reviewer_output.replan = False
        reviewer_worker.create.return_value.invoke.return_value = {
            "structured_response": reviewer_output
        }

        # ---- Mock summarizer via patched create() ----
        mock_summary_output = DummyResponseFormat(result="final summary")
        mock_create.return_value.invoke.return_value = {
            "structured_response": mock_summary_output
        }

        # ---- Instantiate and run supervisor ----
        supervisor = ConfigurableSupervisor(
            name="Supervisor",
            prompt="Supervisor prompt",
            planner=planner_worker,
            reviewer=reviewer_worker,
            response_format=DummyResponseFormat,
            workers=[agent_worker],
        )

        graph = supervisor.create()
        result = graph.invoke({"user_prompt": "Debrief the operation"})

        self.assertEqual(result["structured_response"]["result"], "final summary")
