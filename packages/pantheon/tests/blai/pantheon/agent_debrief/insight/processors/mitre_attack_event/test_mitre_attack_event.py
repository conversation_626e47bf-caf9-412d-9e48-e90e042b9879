# test_mitre_attack_event.py
import pytest
from pydantic import ValidationError

from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.indicators_of_compromise import IndicatorOfCompromise
from blai.pantheon.agent_debrief.insight.techniques.mitre_attack.models.mitre_attack_event import MitreAttackEvent


def test_create_valid_event(base_timestamp, valid_iocs, valid_process_execution):
    event = MitreAttackEvent(
        event_id="evt-2025-0001",
        timestamp=base_timestamp,
        technique_id="T1059.001",
        technique_name="Command and Scripting Interpreter: PowerShell",
        tactic_name="Execution",
        actor="APT-X",
        target_system="T3-AUS-USR-NB-002",
        target_user="GLOBEX\\john.smith",
        detection_source="EDR",
        severity="critical",
        description="Suspicious PowerShell activity",
        references=["https://attack.mitre.org/techniques/T1059/001/"],
        iocs=valid_iocs,
        process_execution=valid_process_execution
    )
    assert event.event_id == "evt-2025-0001"
    assert len(event.iocs) == 5


def test_missing_required_fields():
    with pytest.raises(ValidationError):
        MitreAttackEvent(
            event_id="evt-2025-0002",
            timestamp="2025-01-27T08:30:00Z",
            technique_id="T1059.001",
            technique_name="PowerShell",
            target_system="Host-A"
            # Missing required fields like description, severity, etc.
        )
