import json
import os
import unittest
from unittest.mock import MagicMock, patch

from pydantic import SecretStr

from blai.pantheon.agent_debrief.models import ProcedureRunDetails
from blai.pantheon.agent_debrief.processor import AgentDebriefProcessingRequest, AgentDebriefProcessorSettings, \
    AgentDebriefProcessor
from blai.pantheon.agent_debrief.render_targets import AgentDebriefResponse
from blai.pantheon.agents.configurable.supervisors.agent_debrief import AgentDebrief
from blai.pantheon.settings.azure import AzureOpenAISettings




class TestAgentDebriefProcessor(unittest.TestCase):
    @patch("blai.pantheon.agents.configurable.supervisors.base.ConfigurableSupervisor.create")
    @patch.dict(os.environ, {"AZURE_OPENAI_API_KEY": "fake-api-key"})
    def test_call_invokes_model_and_returns_structured_output(self, mock_create):
        # Mock request and expected return
        mock_procedure_run_output = """
        {
          "id": "g2zwoalhnedxs38el1li71ot",
          "procedureId": "501",
          "procedureName": "Cybersecurity Situational Awareness Report agent_debrief outcome",
          "customName": "Cybersecurity Situational Awareness Report agent_debrief outcome - g2zwoalhnedxs38el1li71ot",
          "longTermMemory": false,
          "outcome": "False Positive",
          "executiveSummary": "The analysis indicates that there is a lack of available data regarding vulnerabilities or major cybersecurity incidents for May 1, 2025. This absence of information suggests a potential gap in monitoring or reporting mechanisms that could impact the organization's risk assessment and response strategies.",
          "keyFindings": "- No vulnerabilities or CVEs were reported for May 1, 2025.\\n- No major cybersecurity incidents were documented for the same date.\\n- The lack of data may indicate a need for improved monitoring and reporting processes.",
          "actionsTaken": "- Recommendations created\\n- Report generated",
          "trigger": "MANUAL",
          "date": "2025-06-05T09:24:07.400Z",
          "status": "HumanReview",
          "failedReason": "",
          "inputs": [
            {
              "name": "date",
              "value": "May 1 2025"
            }
          ],
          "childrenTasks": [
            {
              "id": "awaranf134wu16kp707fsg6w",
              "taskId": 2389,
              "prompt": "What vulnerabilities or CVE's were announced on {date}?",
              "status": "FINISHED",
              "description": "",
              "outcome": "I couldn't find any information regarding vulnerabilities or CVEs announced on May 1, 2025. It appears that there may not be any available data for that specific date. If you have any other questions or need information on a different topic, feel free to ask!",
              "failedReason": "",
              "sources": [],
              "memories": [],
              "tools": [
                {
                  "id": "e99d7974-ad31-4d01-be8c-8331562871d7",
                  "displayName": "Cybersecurity Blogs Data",
                  "type": "publicDataSource"
                },
                {
                  "id": "xm1ad72ergivqqacrkkxjlk5",
                  "displayName": "Threat Intelligence Analyst",
                  "type": "coordinator"
                }
              ]
            },
            {
              "id": "g9w5mk1f8isyprq5gud5cla9",
              "taskId": 2390,
              "prompt": "What major cybersecurity incidents occurred on {date}?",
              "status": "FINISHED",
              "description": "",
              "outcome": "I couldn't find specific information regarding major cybersecurity incidents that occurred on May 1, 2025. It appears that there may not be available data or reports for that date. If you have any other specific dates or topics in mind, please let me know!",
              "failedReason": "",
              "sources": [],
              "memories": [],
              "tools": [
                {
                  "id": "e99d7974-ad31-4d01-be8c-8331562871d7",
                  "displayName": "Cybersecurity Blogs Data",
                  "type": "publicDataSource"
                },
                {
                  "id": "xm1ad72ergivqqacrkkxjlk5",
                  "displayName": "Threat Intelligence Analyst",
                  "type": "coordinator"
                }
              ]
            }
          ],
          "thumbsUpCount": 0,
          "thumbsDownCount": 0,
          "userRating": null,
          "procedureRunId": "g2zwoalhnedxs38el1li71ot"
        }
        """
        details = ProcedureRunDetails(**json.loads(mock_procedure_run_output))
        request = AgentDebriefProcessingRequest(procedure_run_output=details)

        expected_response = AgentDebriefResponse(id="test", elements=[])

        # Setup mock graph returned by .create()
        mock_graph = MagicMock()
        mock_graph.invoke.return_value = {"structured_response": expected_response}
        mock_create.return_value = mock_graph

        # When

        processor = AgentDebriefProcessor(settings=AgentDebriefProcessorSettings())
        result = processor(request)

        # Then
        mock_create.assert_called_once()
        mock_graph.invoke.assert_called_once()
        self.assertEqual(result, expected_response)
