[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "pantheon"
version = "1.0.0"
description = "Bricklayer's Pantheon - Insight and Aggregation Processing"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langchain",
    "langchain-openai",
    "langgraph-supervisor",
    "langchain-tavily",
    "langchain-community",
    "unstructured",
    "langgraph",
    "pydantic",
    "pydantic-settings",
]


[tool.hatch.envs.dev]
dependencies = [
    "mypy",
    "pydantic",
    "pydantic[mypy]",
    "pytest"
]

[tool.hatch.build.targets.wheel]
packages = ["src/blai"]

[tool.uv]
package = true
cache-keys = [{ file = "pyproject.toml" }, { git = true }]
dev-dependencies = [
    "pytest",
    "pytest-asyncio"
]

[tool.pytest.ini_options]
addopts = "-ra"
testpaths = ["tests"]
pythonpath = ["src/blai"]

