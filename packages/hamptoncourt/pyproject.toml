[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "hamptoncourt"
version = "0.1.0"
description = "Bricklayer's Hampton Court Interoperability System"
authors = [{ name = "<PERSON><PERSON><PERSON>dra S Prabhu", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
]

[tool.hatch.build]
sources = ["src/blai"]

[tool.pytest.ini_options]
addopts = "-ra"
testpaths = ["tests"]
pythonpath = ["src/blai"]
