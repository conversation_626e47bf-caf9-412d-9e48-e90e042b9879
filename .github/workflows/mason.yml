name: Create Build artifacts for <PERSON>

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          version: "0.7.13"

      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: "pyproject.toml"

      - name: Enable caching
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      
      - name: Test my workflow
        run: echo Template, Testing!        
