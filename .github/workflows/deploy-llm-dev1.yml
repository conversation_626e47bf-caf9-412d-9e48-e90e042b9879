name: Deploy LLM service to dev1 cluster
on:
  pull_request_target:
    types:
      - closed
    branches:
      - "develop"
    paths:
      - llm_api/**
  workflow_dispatch:
    inputs:
      logLevel:
        description: "Log level"
        required: true
        default: "warning"
        type: choice
        options:
          - info
          - warning
          - debug

jobs:
  build-and-push-image:
    if: github.event.pull_request.merged == true
    runs-on: self-hosted

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup SonarQube
        uses: warchant/setup-sonar-scanner@v7
        
      - name: SonarQube Scan
        run: |
            cd llm_api 
            sonar-scanner \
            -Dsonar.projectKey=${{ secrets.PROJECT_KEY }} \
            -Dsonar.sources=. \
            -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.SONAR_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayerai-llm
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          DOCKER_BUILDKIT=1 docker buildx build \
              --cache-from=type=gha,scope=${{ github.ref_name }}-plugins-service-dev \
              --cache-to=type=gha,mode=max,scope=${{ github.ref_name }}-plugins-service-dev \
              --pull \
              -t $REGISTRY/$REPOSITORY:$IMAGE_TAG \
              -f ./llm_api/Dockerfile.llm_api \
              ./llm_api \
              --load
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Run Trivy scan
        continue-on-error: false
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayerai-llm
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          trivy image --severity HIGH,CRITICAL --ignore-unfixed --security-checks vuln --timeout 10m ${{env.REGISTRY}}/${{env.REPOSITORY}}:${{env.IMAGE_TAG}}
# trivy image --exit-code 1 --severity CRITICAL --no-progress ${{env.REGISTRY}}/${{env.REPOSITORY}}:${{env.IMAGE_TAG}}
      
      - name: Clean Docker Image from self hosted runner
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayerai-llm
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          docker rmi -f $REGISTRY/$REPOSITORY:$IMAGE_TAG

  deploy-image:
    needs: build-and-push-image
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write
      
    steps:
       - name: Checkout repository
         uses: actions/checkout@v3

       - name: Configure AWS Credentials
         uses: aws-actions/configure-aws-credentials@v4
         with:
           role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
           role-session-name: github-actions-session
           aws-region: us-east-1

       - name: Login to Amazon ECR
         id: login-ecr
         uses: aws-actions/amazon-ecr-login@v1
         
       - name: Configure AWS Credentials
         uses: aws-actions/configure-aws-credentials@v4
         with:
          role-to-assume: ${{ secrets.AWS_DEV_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

       - name: Deploy llm-service to cluster
         env:
           ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
           ECR_REPOSITORY: bricklayerai-llm
           IMAGE_TAG: develop-${{ github.sha }}
         run: |
           aws eks update-kubeconfig --region us-east-1 --name development-dev
           kubectl config get-contexts
           kubectl config use-context arn:aws:eks:us-east-1:140023396818:cluster/development-dev
           kubectl apply -f k8s/dev/llm
           kubectl set image deployment/llm-deployment-dev -n development llm-app=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
