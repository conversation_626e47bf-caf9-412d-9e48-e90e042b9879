name: Build & publish LLM aifiltering1
on:
  pull_request_target:
    types:
      - closed
    branches:
      - "create-generic-api-plugin"
    paths:
      - llm_api/**
  workflow_dispatch:
    inputs:
      logLevel:
        description: "Log level"
        required: true
        default: "warning"
        type: choice
        options:
          - info
          - warning
          - debug

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayerai-llm
          IMAGE_TAG: aifiltering-${{ github.sha }}
        run: |
          docker build ./llm_api --pull --no-cache -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -f ./llm_api/Dockerfile
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

#      - name: Deploy llm to cluster
#        env:
#          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
#          ECR_REPOSITORY: bricklayerai-llm
#          IMAGE_TAG: aifiltering-${{ github.sha }}
#        run: |
#          # install kubectl
#          curl -LO "https://dl.k8s.io/release/v1.27.7/bin/linux/amd64/kubectl"
#          chmod +x kubectl
#          mkdir -p ~/.local/bin
#          mv ./kubectl $HOME/.local/bin/kubectl
#          export PATH=$PATH:$HOME/.local/bin

          # create kubectl context
#          aws eks update-kubeconfig --region us-east-1 --name bricklayer-ai-eks-prod
#          kubectl config get-contexts
#          kubectl config use-context arn:aws:eks:us-east-1:607309871017:cluster/bricklayer-ai-eks-prod
#          kubectl set image deployment/llm-deployment-dev-aifiltering -n development llm-app-aifiltering=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
