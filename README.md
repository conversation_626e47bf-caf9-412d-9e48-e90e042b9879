# Bricklayer's <PERSON>orepo 🧱

> Modular systems for data-rich, AI-driven applications — built brick by brick.

---

## 📦 Overview

This monorepo contains multiple Python packages and services, organized for flexible development, deployment, and scaling in Kubernetes environments.

Each project is a self-contained Python package, built with [<PERSON>](https://hatch.pypa.io/), and optionally deployable via Docker and Kubernetes.

---

## 📁 Repository Structure

```
├── projects/ # Python packages (multi-package workspace)
│ ├── pantheon/ # Core task engine and analytics
│ └── hamptoncourt/ # Interoperability and system orchestration
│
├── deploy/ # Kubernetes manifests & deployment config
├── hatch.toml # Global hatch environment and workspace config
├── pyproject.toml # Root-level project tool definitions
└── README.md # This file
```

---

## 🧰 Tooling

- **Packaging & Envs:** [`Hatch`](https://hatch.pypa.io/latest/)
- **Linting:** [`ruff`](https://docs.astral.sh/ruff/), [`black`](https://black.readthedocs.io/)
- **Testing:** [`pytest`](https://docs.pytest.org/)
- **Typing:** [`mypy`](https://mypy-lang.org/)
- **Kubernetes Deployments:** via manifests in `deploy/`

---

## 🚀 Getting Started

### 1. Install Hatch

```bash
pip install hatch
```

