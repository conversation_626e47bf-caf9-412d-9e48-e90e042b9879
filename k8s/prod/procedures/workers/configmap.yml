---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: production
  name: procedures-workers-cm-prod
data:
  AZURE_OPENAI_API_BASE: dummy
  AZURE_OPENAI_API_BASE_GPT_4: dummy
  AZURE_OPENAI_API_KEY: dummy
  AZURE_OPENAI_API_KEY_GPT_4: dummy
  AZURE_OPENAI_DEPLOYMENT: dummy
  AZURE_OPENAI_DEPLOYMENT_GPT4: dummy
  BLAI_ENV: prod
  # FEATURE FLAGS
  FF_ENABLE_AUTORETRY_EXCEPTIONS: "1"
  FF_ENABLE_HEALTH_CHECKS: "1"
  FF_RAISE_SPECIFIC_CONTENT_FILTERING_ERROR: "1"
  PROCEDURES_WORKERS_DEADLOCK_TIMEOUT: "900"  # 15 minutes
  PROCEDURES_WORKERS_LIVENESS_TTL: "300"  # 5 minutes
  PROCEDURES__LLMAPP__URL: http://llm-service-prod:9000
  PROCEDURES__REDIS__URL: redis-production.exhwrq.0001.use1.cache.amazonaws.com:6379
  PROCEDURES__WORKERS__BASEPATH: s3://blai-procedure-runs-production-20250219044702079400000003
  PROCEDURES__WORKERS__REQUEST__TIMEOUT: "10"
  PROCEDURES__WORKERS__WORKERS: "4"
  PROCEDURE_HEALTH_CHECK_DIR: '/tmp/procedure_health_check'
  PUBLIC_BACKEND_URL: http://backend-service-prod:8000
  # sentry
  SENTRY_DSN: https://<EMAIL>/4508738599190528
  SENTRY_PROFILES_SAMPLE_RATE: "0.5"
  SENTRY_TRACES_SAMPLE_RATE: "0.8"
