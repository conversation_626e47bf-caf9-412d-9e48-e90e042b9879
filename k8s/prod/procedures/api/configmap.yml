---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: production
  name: procedures-api-cm-prod
data:
  AZURE_OPENAI_API_BASE: dummy
  AZURE_OPENAI_API_BASE_GPT_4: dummy
  AZURE_OPENAI_API_KEY: dummy
  AZURE_OPENAI_API_KEY_GPT_4: dummy
  AZURE_OPENAI_DEPLOYMENT: dummy
  AZURE_OPENAI_DEPLOYMENT_GPT4: dummy
  BLAI_ENV: prod
  CORS_ORIGIN: "*"
  # FEATURE FLAGS
  PROCEDURES__API__WORKERS: "4"
  PROCEDURES__REDIS__URL: redis-production.exhwrq.0001.use1.cache.amazonaws.com:6379
  PUBLIC_BACKEND_URL: http://backend-service-prod:8000
  # sentry
  SENTRY_DSN: https://<EMAIL>/4508738585296896
  SENTRY_PROFILES_SAMPLE_RATE: "0.5"
  SENTRY_TRACES_SAMPLE_RATE: "0.8"
