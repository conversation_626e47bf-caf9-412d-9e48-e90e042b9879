---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: development
  name: procedures-api-cm-dev
data:
  AZURE_OPENAI_API_BASE: dummy
  AZURE_OPENAI_API_BASE_GPT_4: dummy
  AZURE_OPENAI_API_KEY: dummy
  AZURE_OPENAI_API_KEY_GPT_4: dummy
  AZURE_OPENAI_DEPLOYMENT: dummy
  AZURE_OPENAI_DEPLOYMENT_GPT4: dummy
  BLAI_ENV: dev
  CORS_ORIGIN: "*"
  PROCEDURES__API__WORKERS: "4"
  PROCEDURES__REDIS__URL: redis-development.jj8l5o.0001.use1.cache.amazonaws.com:6379
  PUBLIC_BACKEND_URL: http://backend-service-dev:8000
  SENTRY_DSN: https://<EMAIL>/4508738585296896
  SENTRY_PROFILES_SAMPLE_RATE: "1.0"
  SENTRY_TRACES_SAMPLE_RATE: "1.0"
