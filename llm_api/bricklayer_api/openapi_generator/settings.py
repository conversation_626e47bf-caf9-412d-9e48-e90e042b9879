"""
Default settings for OpenAPI Generator Django app.

These settings can be overridden in the main Django project's settings.py
"""

# Default upload path for OpenAPI spec files
# Can be overridden by setting OPENAPI_GENERATOR_UPLOAD_PATH in main settings
OPENAPI_GENERATOR_UPLOAD_PATH = 'openapi_generator/specs'

# Maximum file size for uploads (in bytes) - default 10MB
OPENAPI_GENERATOR_MAX_FILE_SIZE = 10 * 1024 * 1024

# Allowed file extensions
OPENAPI_GENERATOR_ALLOWED_EXTENSIONS = ['.json', '.yaml', '.yml']

# LLM API settings (for future use)
OPENAPI_GENERATOR_LLM_API_URL = 'http://llm_api:9000'
OPENAPI_GENERATOR_LLM_TIMEOUT = 300  # 5 minutes

# Plugin testing settings (for future use)
OPENAPI_GENERATOR_PLUGIN_TEST_ENABLED = True
OPENAPI_GENERATOR_PLUGIN_SERVICE_URL = 'http://plugin_service:8080'
