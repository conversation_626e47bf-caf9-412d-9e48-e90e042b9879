# OpenAPI Plugin Generator

A Django app integrated into the LLM API project for generating Bricklayer plugin specifications from OpenAPI specs and user intent.

## Overview

This app allows users to:
1. Submit OpenAPI/Swagger specifications along with their intent
2. Generate Bricklayer-compatible plugin specifications using LLM processing
3. Test the generated plugins automatically
4. Track processing status and logs

## Integration

The app is integrated into the LLM API project as a Django app:

- **Location**: `llm_api/bricklayer_api/openapi_generator/`
- **URLs**: `/api/openapi/plugin-generation/`
- **Settings**: Added to `INSTALLED_APPS` in `bricklayer_api.settings.common`

## API Endpoints

### Create Plugin Generation Request
```bash
POST /api/openapi/plugin-generation/
Content-Type: application/json

{
  "service_name": "VirusTotal",
  "openapi_spec": {
    "openapi": "3.0.0",
    "info": {"title": "VirusTotal API", "version": "1.0"},
    "paths": {...}
  },
  "intent": "I require IP and host related API endpoints"
}
```

### Check Request Status
```bash
GET /api/openapi/plugin-generation/{id}/
```

### Get Processing Logs
```bash
GET /api/openapi/plugin-generation/{id}/logs/
```

### Retry Failed Request
```bash
POST /api/openapi/plugin-generation/{id}/retry/
```

### Health Check
```bash
GET /api/openapi/plugin-generation/health/
```

### Statistics
```bash
GET /api/openapi/plugin-generation/stats/
```

## Models

### PluginGenerationRequest
- Stores OpenAPI specs, user intent, and processing status
- Tracks generated plugin specifications and test results
- Maintains processing timestamps and error messages

### ProcessingLog
- Detailed logs for each processing step
- Includes structured data for debugging
- Linked to parent request

## Processing Flow

1. **Request Creation**: User submits OpenAPI spec + intent
2. **LLM Processing**: Extract relevant endpoints based on intent
3. **Plugin Generation**: Create Bricklayer-compatible plugin spec
4. **Testing**: Validate plugin through Plugin Service
5. **Completion**: Store results and mark as completed

## Usage Example

```python
import requests

# Create request
response = requests.post('http://localhost:8001/api/openapi/plugin-generation/', json={
    "service_name": "VirusTotal",
    "openapi_spec": {...},
    "intent": "I need IP analysis endpoints"
})

request_id = response.json()['id']

# Check status
status = requests.get(f'http://localhost:8001/api/openapi/plugin-generation/{request_id}/')
print(status.json()['status'])
```

## Testing

Run the integration test:
```bash
python test_openapi_generator.py
```

## Admin Interface

Access the Django admin at `/admin/` to:
- View all plugin generation requests
- Monitor processing logs
- Retry failed requests
- View generated specifications

## Configuration

The app uses the existing LLM API infrastructure:
- **Database**: PostgreSQL (shared with other apps)
- **Celery**: For async processing (shared broker)
- **Logging**: Integrated with Django logging

## Development

To extend the app:
1. Add new fields to models (create migrations)
2. Extend serializers for new API fields
3. Add custom processing logic in `services.py`
4. Create new API endpoints in `views.py`

## Future Enhancements

- [ ] Integration with actual LLM API for spec generation
- [ ] Real Plugin Service testing integration
- [ ] Advanced filtering based on intent analysis
- [ ] Plugin spec validation and optimization
- [ ] Batch processing for multiple specs
