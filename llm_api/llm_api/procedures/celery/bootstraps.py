import os
from pathlib import Path
from time import time

from celery import bootsteps
from filelock import FileLock

from llm_api.procedures.celery.constants import LIVENESS_FILE

DEADLOCK_TIMEOUT = int(os.getenv("PROCEDURES_WORKERS_DEADLOCK_TIMEOUT", "900"))


class LivenessProbeStep(bootsteps.StartStopStep):
    requires = {"celery.worker.components:Timer"}

    def __init__(self, worker, **kwargs):
        super().__init__(worker, **kwargs)
        self.requests = []
        self.liveness_tref = None

    def start(self, worker):
        self.liveness_tref = worker.timer.call_repeatedly(
            5, self.update_liveness_file, priority=10
        )

    def update_liveness_file(self):
        with FileLock(f"{LIVENESS_FILE}.lock"):
            Path(LIVENESS_FILE).touch()

    def stop(self, _):
        if self.liveness_tref:
            self.liveness_tref.cancel()
            self.liveness_tref = None


class DeadlockDetectionStep(bootsteps.StartStopStep):
    requires = {"celery.worker.components:Timer"}

    def __init__(self, worker, **_):
        super().__init__(worker, **_)
        self.requests = []
        self.task_monitoring_tref = None

    def start(self, worker):
        self.task_monitoring_tref = worker.timer.call_repeatedly(
            30, self.monitor_worker_activity, args=(worker,), priority=10
        )

    def monitor_worker_activity(self, worker):
        for task_req in worker.state.active_requests:
            now = int(time())
            if task_req.time_start and now - task_req.time_start > DEADLOCK_TIMEOUT:
                print(
                    f"Task {task_req.name}[{task_req.id}] hasn't finished in over "
                    f"{int(DEADLOCK_TIMEOUT / 60)} minutes. Restarting worker ..."
                )
                raise SystemExit(1)

    def stop(self, _):
        if self.task_monitoring_tref:
            self.task_monitoring_tref.cancel()
            self.task_monitoring_tref = None
