"""
API views for the OpenAPI Plugin Generator app.
"""

import logging
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone

from .models import PluginGenerationRequest, ProcessingLog
from .serializers import (
    PluginGenerationRequestSerializer,
    PluginGenerationCreateSerializer,
    PluginGenerationListSerializer,
    ProcessingLogSerializer
)
from .services import PluginGenerationService

logger = logging.getLogger(__name__)


class PluginGenerationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing plugin generation requests.

    Provides CRUD operations and additional actions for plugin generation workflow.
    Supports both JSON and multipart/form-data for file uploads.
    """

    queryset = PluginGenerationRequest.objects.all()
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access for testing
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return PluginGenerationCreateSerializer
        elif self.action == 'list':
            return PluginGenerationListSerializer
        return PluginGenerationRequestSerializer
    
    def get_queryset(self):
        """Filter queryset based on user and query parameters."""
        queryset = PluginGenerationRequest.objects.all()
        
        # Filter by user if authenticated
        if self.request.user.is_authenticated:
            queryset = queryset.filter(
                Q(user=self.request.user) | Q(user__isnull=True)
            )
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by service name
        service_filter = self.request.query_params.get('service')
        if service_filter:
            queryset = queryset.filter(service_name__icontains=service_filter)
        
        return queryset.order_by('-created_at')
    
    def create(self, request: Request) -> Response:
        """
        Create a new plugin generation request.
        
        Args:
            request: HTTP request object
            
        Returns:
            Response with created request data
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Save the request
        instance = serializer.save(
            user=request.user if request.user.is_authenticated else None
        )

        logger.info(
            f"Created plugin generation request {instance.id} for service {instance.service_name} "
            f"with file {instance.original_filename} ({instance.file_type})"
        )
        
        # Start async processing only if parsing was successful
        if instance.status == 'pending' and instance.openapi_spec:
            try:
                PluginGenerationService.start_processing(str(instance.id))
                logger.info(f"Started processing for request {instance.id}")
            except Exception as e:
                logger.error(f"Failed to start processing for request {instance.id}: {str(e)}")
                instance.mark_failed(f"Failed to start processing: {str(e)}")
        elif instance.status == 'failed':
            logger.warning(f"Skipping processing for request {instance.id} due to parsing failure: {instance.error_message}")
        
        # Return full serialized data
        response_serializer = PluginGenerationRequestSerializer(instance)
        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['get'])
    def logs(self, request: Request, pk: str = None) -> Response:
        """
        Get processing logs for a specific request.
        
        Args:
            request: HTTP request object
            pk: Primary key of the request
            
        Returns:
            Response with processing logs
        """
        instance = get_object_or_404(PluginGenerationRequest, pk=pk)
        
        # Check permissions
        if (request.user.is_authenticated and 
            instance.user and 
            instance.user != request.user and 
            not request.user.is_staff):
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        logs = instance.logs.all().order_by('timestamp')
        serializer = ProcessingLogSerializer(logs, many=True)
        
        return Response({
            'request_id': str(instance.id),
            'service_name': instance.service_name,
            'status': instance.status,
            'logs': serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def retry(self, request: Request, pk: str = None) -> Response:
        """
        Retry a failed plugin generation request.
        
        Args:
            request: HTTP request object
            pk: Primary key of the request
            
        Returns:
            Response with retry status
        """
        instance = get_object_or_404(PluginGenerationRequest, pk=pk)
        
        # Check permissions
        if (request.user.is_authenticated and 
            instance.user and 
            instance.user != request.user and 
            not request.user.is_staff):
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if request can be retried
        if instance.status not in ['failed']:
            return Response(
                {'error': f'Cannot retry request with status: {instance.status}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Reset status and clear error message
        instance.status = 'pending'
        instance.error_message = None
        instance.processing_started_at = None
        instance.processing_completed_at = None
        instance.save(update_fields=[
            'status', 'error_message', 'processing_started_at', 
            'processing_completed_at', 'updated_at'
        ])
        
        # Start processing again
        try:
            PluginGenerationService.start_processing(str(instance.id))
            logger.info(f"Retrying processing for request {instance.id}")
            
            return Response({
                'message': 'Processing restarted',
                'request_id': str(instance.id),
                'status': instance.status
            })
        except Exception as e:
            error_msg = f"Failed to restart processing: {str(e)}"
            instance.mark_failed(error_msg)
            logger.error(f"Failed to retry request {instance.id}: {error_msg}")
            
            return Response(
                {'error': error_msg}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def health(self, request: Request) -> Response:
        """
        Health check endpoint for the plugin generation service.
        
        Args:
            request: HTTP request object
            
        Returns:
            Response with health status
        """
        health_data = {
            'status': 'healthy',
            'timestamp': timezone.now(),
            'version': '1.0.0',
        }
        
        # Check database connectivity
        try:
            PluginGenerationRequest.objects.count()
            health_data['database'] = 'connected'
        except Exception as e:
            health_data['database'] = f'error: {str(e)}'
            health_data['status'] = 'unhealthy'
        
        return Response(health_data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request: Request) -> Response:
        """
        Get statistics about plugin generation requests.
        
        Args:
            request: HTTP request object
            
        Returns:
            Response with statistics
        """
        from django.db.models import Count
        
        # Get status counts
        status_counts = PluginGenerationRequest.objects.values('status').annotate(
            count=Count('status')
        ).order_by('status')
        
        # Get recent activity (last 24 hours)
        from datetime import timedelta
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_count = PluginGenerationRequest.objects.filter(
            created_at__gte=recent_cutoff
        ).count()
        
        # Get service counts
        service_counts = PluginGenerationRequest.objects.values('service_name').annotate(
            count=Count('service_name')
        ).order_by('-count')[:10]  # Top 10 services
        
        stats_data = {
            'total_requests': PluginGenerationRequest.objects.count(),
            'recent_requests_24h': recent_count,
            'status_breakdown': {item['status']: item['count'] for item in status_counts},
            'top_services': list(service_counts),
        }
        
        return Response(stats_data)
