"""
Core service logic for plugin generation processing.
"""

import logging
import requests
from typing import Dict, Any, Optional
from django.utils import timezone
from django.conf import settings

from .models import PluginGenerationRequest, ProcessingLog

logger = logging.getLogger(__name__)


class PluginGenerationService:
    """
    Main service class for handling plugin generation workflow.
    """
    
    @classmethod
    def start_processing(cls, request_id: str) -> None:
        """
        Start synchronous processing of plugin generation.

        Args:
            request_id: UUID of the plugin generation request
        """
        logger.info(f"Starting synchronous processing for request {request_id}")
        cls.process_request(request_id)
    
    @classmethod
    def process_request(cls, request_id: str) -> None:
        """
        Main processing pipeline for plugin generation.
        
        Args:
            request_id: UUID of the plugin generation request
        """
        request_obj = None
        try:
            # Get the request object
            request_obj = PluginGenerationRequest.objects.get(id=request_id)
            
            # Mark as processing
            request_obj.mark_processing_started()
            cls._log_step(request_obj, 'processing_started', 'Started plugin generation')
            
            # Step 1: Generate plugin spec via LLM
            plugin_spec = cls._generate_plugin_spec(request_obj)
            
            # Step 2: Save generated spec
            request_obj.generated_plugin_spec = plugin_spec
            request_obj.save(update_fields=['generated_plugin_spec', 'updated_at'])
            
            cls._log_step(
                request_obj, 
                'spec_generated', 
                'Plugin spec generated successfully',
                data={'spec_size': len(str(plugin_spec))}
            )
            
            # Step 3: Test plugin via Plugin Service
            test_results = cls._test_plugin(request_obj, plugin_spec)
            
            # Step 4: Mark as completed
            request_obj.mark_completed(plugin_spec=plugin_spec, test_results=test_results)
            
            cls._log_step(
                request_obj, 
                'completed', 
                'Plugin generation completed successfully',
                data={'test_results_summary': cls._summarize_test_results(test_results)}
            )
            
            logger.info(f"Successfully processed request {request_id}")
            
        except Exception as e:
            error_msg = f"Processing failed: {str(e)}"
            logger.exception(f"Error processing request {request_id}: {error_msg}")
            
            if request_obj:
                request_obj.mark_failed(error_msg)
                cls._log_step(request_obj, 'failed', error_msg)
    
    @classmethod
    def _generate_plugin_spec(cls, request_obj: PluginGenerationRequest) -> Dict[str, Any]:
        """
        Generate plugin spec using LLM processing.

        Args:
            request_obj: Plugin generation request object

        Returns:
            Generated plugin specification in Bricklayer format
        """
        cls._log_step(request_obj, 'llm_call_start', 'Starting LLM processing for plugin spec generation')

        try:
            # Prepare LLM prompt
            prompt = cls._create_llm_prompt(request_obj.openapi_spec, request_obj.intent, request_obj.service_name)

            # Call LLM API
            llm_response = cls._call_llm_api(prompt)

            # Parse LLM response into Bricklayer format
            plugin_spec = cls._parse_llm_response_to_bricklayer_format(
                llm_response,
                request_obj.service_name,
                request_obj.openapi_spec
            )

            cls._log_step(
                request_obj,
                'llm_call_success',
                'Plugin spec generated successfully',
                data={
                    'endpoints_count': len(plugin_spec.get('spec', {}).get('endpoints', [])),
                    'llm_response_length': len(str(llm_response))
                }
            )

            return plugin_spec

        except Exception as e:
            cls._log_step(
                request_obj,
                'llm_call_failed',
                f'LLM processing failed: {str(e)}',
                data={'error': str(e)}
            )
            raise
    
    @classmethod
    def _test_plugin(cls, request_obj: PluginGenerationRequest, plugin_spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Test plugin using Plugin Service.
        
        Args:
            request_obj: Plugin generation request object
            plugin_spec: Generated plugin specification
            
        Returns:
            Test results
        """
        cls._log_step(request_obj, 'testing_start', 'Starting plugin testing')
        
        # Update status to testing
        request_obj.status = 'testing'
        request_obj.save(update_fields=['status', 'updated_at'])
        
        # For now, return mock test results
        # This will be replaced with actual Plugin Service calls
        test_results = {
            "status": "passed",
            "total_tests": 3,
            "passed_tests": 3,
            "failed_tests": 0,
            "test_details": [
                {"test": "endpoint_validation", "status": "passed"},
                {"test": "authentication_check", "status": "passed"},
                {"test": "response_format", "status": "passed"}
            ]
        }
        
        cls._log_step(
            request_obj, 
            'testing_complete', 
            'Plugin testing completed',
            data={'test_summary': cls._summarize_test_results(test_results)}
        )
        
        return test_results

    @classmethod
    def _create_llm_prompt(cls, openapi_spec: Dict[str, Any], intent: str, service_name: str) -> str:
        """
        Create LLM prompt for plugin generation.

        Args:
            openapi_spec: OpenAPI specification
            intent: User's intent
            service_name: Name of the service

        Returns:
            Formatted prompt for LLM
        """
        prompt = f"""
You are an expert API integration specialist. Your task is to analyze an OpenAPI specification and generate a Bricklayer plugin specification based on the user's intent.

**Service Name:** {service_name}

**User Intent:** {intent}

**OpenAPI Specification:**
{cls._format_openapi_for_prompt(openapi_spec)}

**Task:**
1. Analyze the OpenAPI specification
2. Filter endpoints that match the user's intent
3. Generate a Bricklayer plugin specification in the exact format below

**Required Output Format (JSON):**
{{
  "name": "Service Name Plugin",
  "description": "Brief description of what this plugin does",
  "spec": {{
    "base_url": "https://api.example.com",
    "endpoints": [
      {{
        "path": "/endpoint/{{param}}",
        "method": "GET",
        "summary": "Endpoint description",
        "parameters": [
          {{
            "name": "param",
            "in": "path",
            "required": true,
            "type": "string",
            "description": "Parameter description"
          }}
        ],
        "responses": {{
          "200": {{
            "description": "Success response description"
          }}
        }}
      }}
    ],
    "authentication": {{
      "type": "apiKey",
      "name": "x-api-key",
      "in": "header"
    }}
  }}
}}

**Important:**
- Only include endpoints that are relevant to the user's intent
- Use the exact JSON structure shown above
- Include proper authentication details from the OpenAPI spec
- Ensure all required fields are present
- Return ONLY the JSON, no additional text

Generate the Bricklayer plugin specification now:
"""
        return prompt

    @classmethod
    def _format_openapi_for_prompt(cls, openapi_spec: Dict[str, Any]) -> str:
        """
        Format OpenAPI spec for LLM prompt (truncated for token efficiency).
        """
        import json
        # Include only essential parts to save tokens
        essential_spec = {
            "openapi": openapi_spec.get("openapi"),
            "info": openapi_spec.get("info"),
            "servers": openapi_spec.get("servers", []),
            "paths": openapi_spec.get("paths", {}),
            "components": {
                "securitySchemes": openapi_spec.get("components", {}).get("securitySchemes", {})
            }
        }
        return json.dumps(essential_spec, indent=2)

    @classmethod
    def _call_llm_api(cls, prompt: str) -> Dict[str, Any]:
        """
        Call the LLM API with the given prompt.

        Args:
            prompt: The prompt to send to LLM

        Returns:
            LLM response
        """
        llm_api_url = getattr(settings, 'OPENAPI_GENERATOR_LLM_API_URL', 'http://llm_api:9000')
        timeout = getattr(settings, 'OPENAPI_GENERATOR_LLM_TIMEOUT', 300)

        import uuid
        payload = {
            "message": prompt,
            "messageId": str(uuid.uuid4()),  # Required field - generate unique ID
            "organizationId": "default-org",  # Required field - use default organization
            "aiFilteringPayload": {
                "components": []
            }
        }

        logger.info(f"🔥 CALLING LLM API at {llm_api_url}/message")
        logger.info(f"🔥 Payload size: {len(str(payload))} characters")
        logger.info(f"🔥 Prompt preview: {prompt[:200]}...")

        response = requests.post(
            f"{llm_api_url}/message",
            json=payload,
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )

        logger.info(f"🔥 LLM API Response Status: {response.status_code}")
        logger.info(f"🔥 LLM API Response Size: {len(response.text)} characters")

        if response.status_code != 200:
            raise Exception(f"LLM API call failed: {response.status_code} - {response.text}")

        llm_result = response.json()
        logger.info(f"🔥 LLM Response Keys: {list(llm_result.keys())}")
        return llm_result

    @classmethod
    def _parse_llm_response_to_bricklayer_format(cls, llm_response: Dict[str, Any], service_name: str, openapi_spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse LLM response and ensure it matches Bricklayer format.

        Args:
            llm_response: Response from LLM API
            service_name: Name of the service
            openapi_spec: Original OpenAPI spec

        Returns:
            Bricklayer-formatted plugin specification
        """
        import json

        # Extract the actual response content
        llm_content = llm_response.get('response', llm_response.get('message', ''))

        logger.info(f"🔥 LLM Response Keys: {list(llm_response.keys())}")
        logger.info(f"🔥 LLM Content Type: {type(llm_content)}")
        logger.info(f"🔥 LLM Content Preview: {str(llm_content)[:500]}...")

        try:
            # Try to parse JSON from LLM response
            if isinstance(llm_content, str):
                # Clean up the response (remove markdown code blocks if present)
                llm_content = llm_content.strip()
                if llm_content.startswith('```json'):
                    llm_content = llm_content[7:]
                if llm_content.endswith('```'):
                    llm_content = llm_content[:-3]
                llm_content = llm_content.strip()

                plugin_spec = json.loads(llm_content)
            else:
                plugin_spec = llm_content

            # Validate and ensure required structure
            if not isinstance(plugin_spec, dict):
                raise ValueError("LLM response is not a valid JSON object")

            # Ensure required fields exist
            if 'name' not in plugin_spec:
                plugin_spec['name'] = f"{service_name} Plugin"

            if 'description' not in plugin_spec:
                plugin_spec['description'] = f"Generated plugin for {service_name}"

            if 'spec' not in plugin_spec:
                plugin_spec['spec'] = {}

            # Ensure spec has required fields
            spec = plugin_spec['spec']
            if 'endpoints' not in spec:
                spec['endpoints'] = []

            if 'base_url' not in spec:
                # Extract base URL from OpenAPI spec
                servers = openapi_spec.get('servers', [])
                spec['base_url'] = servers[0]['url'] if servers else 'https://api.example.com'

            return plugin_spec

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse LLM response: {str(e)}")
            logger.error(f"LLM response content: {llm_content}")

            # Fallback: create a basic plugin spec
            return {
                "name": f"{service_name} Plugin",
                "description": f"Generated plugin for {service_name} (LLM parsing failed)",
                "spec": {
                    "base_url": openapi_spec.get('servers', [{}])[0].get('url', 'https://api.example.com'),
                    "endpoints": [],
                    "authentication": cls._extract_auth_info(openapi_spec)
                }
            }

    @classmethod
    def _extract_relevant_endpoints(cls, openapi_spec: Dict[str, Any], intent: str) -> list:
        """Extract relevant endpoints based on intent"""
        paths = openapi_spec.get('paths', {})
        endpoints = []
        
        # Simple keyword matching for now
        # This will be replaced with LLM-based filtering
        intent_lower = intent.lower()
        keywords = ['ip', 'host', 'domain', 'address'] if 'ip' in intent_lower or 'host' in intent_lower else []
        
        for path, methods in paths.items():
            if any(keyword in path.lower() for keyword in keywords) or not keywords:
                for method, details in methods.items():
                    if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                        endpoints.append({
                            "path": path,
                            "method": method.upper(),
                            "summary": details.get('summary', ''),
                            "description": details.get('description', ''),
                            "parameters": details.get('parameters', [])
                        })
        
        return endpoints
    
    @classmethod
    def _extract_auth_info(cls, openapi_spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication information"""
        security_schemes = openapi_spec.get('components', {}).get('securitySchemes', {})
        security = openapi_spec.get('security', [])
        
        return {
            "schemes": security_schemes,
            "requirements": security
        }
    
    @classmethod
    def _extract_base_url(cls, openapi_spec: Dict[str, Any]) -> str:
        """Extract base URL from servers"""
        servers = openapi_spec.get('servers', [])
        if servers and isinstance(servers[0], dict):
            return servers[0].get('url', '')
        return ''
    
    @classmethod
    def _log_step(cls, request_obj: PluginGenerationRequest, step: str, message: str, data: Optional[Dict] = None) -> None:
        """
        Log processing step to database.
        
        Args:
            request_obj: Plugin generation request object
            step: Step identifier
            message: Log message
            data: Optional additional data
        """
        try:
            ProcessingLog.objects.create(
                request=request_obj,
                step=step,
                message=message,
                data=data
            )
            logger.debug(f"Logged step '{step}' for request {request_obj.id}: {message}")
        except Exception as e:
            logger.error(f"Failed to log step '{step}' for request {request_obj.id}: {str(e)}")
    
    @classmethod
    def _summarize_test_results(cls, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of test results.
        
        Args:
            test_results: Full test results
            
        Returns:
            Summarized test results
        """
        if not test_results:
            return {'status': 'no_results'}
        
        summary = {
            'status': test_results.get('status', 'unknown'),
            'total_tests': test_results.get('total_tests', 0),
            'passed_tests': test_results.get('passed_tests', 0),
            'failed_tests': test_results.get('failed_tests', 0),
        }
        
        if 'errors' in test_results:
            summary['error_count'] = len(test_results['errors'])
        
        return summary
