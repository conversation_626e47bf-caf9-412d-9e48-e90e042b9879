"""
Django admin configuration for the OpenAPI Plugin Generator app.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
import json

from .models import PluginGenerationRequest, ProcessingLog


class ProcessingLogInline(admin.TabularInline):
    """Inline admin for processing logs."""
    model = ProcessingLog
    extra = 0
    readonly_fields = ['step', 'message', 'data', 'timestamp']
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(PluginGenerationRequest)
class PluginGenerationRequestAdmin(admin.ModelAdmin):
    """Admin interface for plugin generation requests."""
    
    list_display = [
        'service_name', 
        'status_badge', 
        'user', 
        'created_at', 
        'processing_duration_display',
        'logs_count'
    ]
    
    list_filter = [
        'status', 
        'created_at', 
        'processing_started_at',
        'service_name'
    ]
    
    search_fields = [
        'service_name', 
        'intent', 
        'user__username',
        'user__email'
    ]
    
    readonly_fields = [
        'id',
        'created_at', 
        'updated_at', 
        'processing_started_at',
        'processing_completed_at',
        'processing_duration_display',
        'openapi_spec_preview',
        'generated_plugin_spec_preview',
        'test_results_preview'
    ]
    
    fieldsets = [
        ('Basic Information', {
            'fields': [
                'id',
                'user',
                'service_name',
                'status',
                'intent'
            ]
        }),
        ('OpenAPI Specification', {
            'fields': ['openapi_spec_preview'],
            'classes': ['collapse']
        }),
        ('Generated Plugin Specification', {
            'fields': ['generated_plugin_spec_preview'],
            'classes': ['collapse']
        }),
        ('Test Results', {
            'fields': ['test_results_preview'],
            'classes': ['collapse']
        }),
        ('Processing Information', {
            'fields': [
                'processing_started_at',
                'processing_completed_at',
                'processing_duration_display',
                'error_message'
            ]
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        })
    ]
    
    inlines = [ProcessingLogInline]
    
    actions = ['retry_failed_requests', 'mark_as_failed']
    
    def status_badge(self, obj):
        """Display status as a colored badge."""
        colors = {
            'pending': '#ffc107',      # yellow
            'processing': '#007bff',   # blue
            'testing': '#17a2b8',      # teal
            'completed': '#28a745',    # green
            'failed': '#dc3545',       # red
        }
        color = colors.get(obj.status, '#6c757d')  # default gray
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 12px;">{}</span>',
            color,
            obj.status.upper()
        )
    status_badge.short_description = 'Status'
    
    def processing_duration_display(self, obj):
        """Display processing duration in a readable format."""
        duration = obj.processing_duration
        if duration:
            total_seconds = int(duration.total_seconds())
            minutes, seconds = divmod(total_seconds, 60)
            hours, minutes = divmod(minutes, 60)
            
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return "-"
    processing_duration_display.short_description = 'Duration'
    
    def logs_count(self, obj):
        """Display count of processing logs."""
        count = obj.logs.count()
        if count > 0:
            url = reverse('admin:openapi_generator_processinglog_changelist')
            return format_html(
                '<a href="{}?request__id__exact={}">{} logs</a>',
                url, obj.id, count
            )
        return "0 logs"
    logs_count.short_description = 'Logs'
    
    def openapi_spec_preview(self, obj):
        """Display a preview of the OpenAPI spec."""
        if obj.openapi_spec:
            try:
                formatted_json = json.dumps(obj.openapi_spec, indent=2)[:1000]
                if len(formatted_json) >= 1000:
                    formatted_json += "\n... (truncated)"
                return format_html('<pre style="max-height: 300px; overflow: auto;">{}</pre>', formatted_json)
            except Exception:
                return "Invalid JSON"
        return "No specification"
    openapi_spec_preview.short_description = 'OpenAPI Specification'
    
    def generated_plugin_spec_preview(self, obj):
        """Display a preview of the generated plugin spec."""
        if obj.generated_plugin_spec:
            try:
                formatted_json = json.dumps(obj.generated_plugin_spec, indent=2)[:1000]
                if len(formatted_json) >= 1000:
                    formatted_json += "\n... (truncated)"
                return format_html('<pre style="max-height: 300px; overflow: auto;">{}</pre>', formatted_json)
            except Exception:
                return "Invalid JSON"
        return "Not generated yet"
    generated_plugin_spec_preview.short_description = 'Generated Plugin Specification'
    
    def test_results_preview(self, obj):
        """Display a preview of the test results."""
        if obj.test_results:
            try:
                formatted_json = json.dumps(obj.test_results, indent=2)[:1000]
                if len(formatted_json) >= 1000:
                    formatted_json += "\n... (truncated)"
                return format_html('<pre style="max-height: 300px; overflow: auto;">{}</pre>', formatted_json)
            except Exception:
                return "Invalid JSON"
        return "No test results"
    test_results_preview.short_description = 'Test Results'
    
    def retry_failed_requests(self, request, queryset):
        """Admin action to retry failed requests."""
        from .services import PluginGenerationService
        
        failed_requests = queryset.filter(status='failed')
        count = 0
        
        for req in failed_requests:
            try:
                req.status = 'pending'
                req.error_message = None
                req.processing_started_at = None
                req.processing_completed_at = None
                req.save()
                
                PluginGenerationService.start_processing(str(req.id))
                count += 1
            except Exception as e:
                self.message_user(request, f"Failed to retry request {req.id}: {str(e)}", level='ERROR')
        
        if count > 0:
            self.message_user(request, f"Successfully restarted {count} failed requests.")
    retry_failed_requests.short_description = "Retry selected failed requests"
    
    def mark_as_failed(self, request, queryset):
        """Admin action to mark requests as failed."""
        processing_requests = queryset.filter(status__in=['pending', 'processing', 'testing'])
        count = processing_requests.update(
            status='failed',
            error_message='Manually marked as failed by admin'
        )
        
        if count > 0:
            self.message_user(request, f"Marked {count} requests as failed.")
    mark_as_failed.short_description = "Mark selected requests as failed"


@admin.register(ProcessingLog)
class ProcessingLogAdmin(admin.ModelAdmin):
    """Admin interface for processing logs."""
    
    list_display = [
        'request_link',
        'step',
        'message_preview',
        'timestamp'
    ]
    
    list_filter = [
        'step',
        'timestamp',
        'request__status',
        'request__service_name'
    ]
    
    search_fields = [
        'message',
        'step',
        'request__service_name',
        'request__id'
    ]
    
    readonly_fields = [
        'request',
        'step',
        'message',
        'data_preview',
        'timestamp'
    ]
    
    def request_link(self, obj):
        """Display link to the associated request."""
        url = reverse('admin:openapi_generator_plugingenerationrequest_change', args=[obj.request.id])
        return format_html('<a href="{}">{}</a>', url, obj.request.service_name)
    request_link.short_description = 'Request'
    
    def message_preview(self, obj):
        """Display truncated message."""
        if len(obj.message) > 50:
            return obj.message[:50] + "..."
        return obj.message
    message_preview.short_description = 'Message'
    
    def data_preview(self, obj):
        """Display formatted additional data."""
        if obj.data:
            try:
                formatted_json = json.dumps(obj.data, indent=2)
                return format_html('<pre style="max-height: 300px; overflow: auto;">{}</pre>', formatted_json)
            except Exception:
                return "Invalid JSON"
        return "No additional data"
    data_preview.short_description = 'Additional Data'
