import json
import yaml
import os
from rest_framework import serializers
from django.conf import settings
from .models import PluginGenerationRequest, ProcessingLog


class ProcessingLogSerializer(serializers.ModelSerializer):
    """Serializer for processing logs"""
    
    class Meta:
        model = ProcessingLog
        fields = ['step', 'message', 'data', 'timestamp']
        read_only_fields = ['step', 'message', 'data', 'timestamp']


class PluginGenerationRequestSerializer(serializers.ModelSerializer):
    """Main serializer for plugin generation requests"""
    
    processing_duration = serializers.ReadOnlyField()
    logs_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PluginGenerationRequest
        fields = [
            'id', 'service_name', 'uploaded_file', 'original_filename', 'file_type',
            'openapi_spec', 'intent', 'status', 'generated_plugin_spec', 'test_results',
            'error_message', 'created_at', 'updated_at', 'processing_started_at',
            'processing_completed_at', 'processing_duration', 'logs_count'
        ]
        read_only_fields = [
            'id', 'original_filename', 'file_type', 'openapi_spec', 'status',
            'generated_plugin_spec', 'test_results', 'error_message', 'created_at',
            'updated_at', 'processing_started_at', 'processing_completed_at',
            'processing_duration', 'logs_count'
        ]
    
    def get_logs_count(self, obj):
        """Get count of processing logs"""
        return obj.logs.count()


class PluginGenerationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new plugin generation requests with file upload"""

    # File upload field
    uploaded_file = serializers.FileField(
        help_text="OpenAPI/Swagger specification file (.json, .yaml, .yml)"
    )

    class Meta:
        model = PluginGenerationRequest
        fields = ['service_name', 'uploaded_file', 'intent']
    
    def validate_uploaded_file(self, value):
        """Validate uploaded OpenAPI spec file"""
        if not value:
            raise serializers.ValidationError("File is required")

        # Get file extension
        file_name = value.name.lower()
        allowed_extensions = getattr(settings, 'OPENAPI_GENERATOR_ALLOWED_EXTENSIONS', ['.json', '.yaml', '.yml'])

        if not any(file_name.endswith(ext) for ext in allowed_extensions):
            raise serializers.ValidationError(
                f"Invalid file type. Allowed extensions: {', '.join(allowed_extensions)}"
            )

        # Check file size
        max_size = getattr(settings, 'OPENAPI_GENERATOR_MAX_FILE_SIZE', 10 * 1024 * 1024)  # 10MB default
        if value.size > max_size:
            raise serializers.ValidationError(
                f"File too large. Maximum size: {max_size / (1024*1024):.1f}MB"
            )

        # Try to parse the file content
        try:
            content = value.read().decode('utf-8')
            value.seek(0)  # Reset file pointer

            if file_name.endswith('.json'):
                parsed_content = json.loads(content)
            elif file_name.endswith(('.yaml', '.yml')):
                parsed_content = yaml.safe_load(content)
            else:
                raise serializers.ValidationError("Unsupported file format")

            # Basic OpenAPI validation
            if not isinstance(parsed_content, dict):
                raise serializers.ValidationError("File must contain a valid OpenAPI specification object")

            # Check required OpenAPI fields
            required_fields = ['openapi', 'info', 'paths']
            for field in required_fields:
                if field not in parsed_content:
                    raise serializers.ValidationError(f"Missing required OpenAPI field: {field}")

            # Validate OpenAPI version
            openapi_version = parsed_content.get('openapi', '')
            if not str(openapi_version).startswith('3.'):
                raise serializers.ValidationError("Only OpenAPI 3.x specifications are supported")

        except json.JSONDecodeError as e:
            raise serializers.ValidationError(f"Invalid JSON format: {str(e)}")
        except yaml.YAMLError as e:
            raise serializers.ValidationError(f"Invalid YAML format: {str(e)}")
        except UnicodeDecodeError:
            raise serializers.ValidationError("File must be UTF-8 encoded")
        except Exception as e:
            raise serializers.ValidationError(f"Error parsing file: {str(e)}")

        return value

    def validate_service_name(self, value):
        """Validate service name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        return value.strip()
    
    def validate_intent(self, value):
        """Validate intent"""
        if not value or not value.strip():
            raise serializers.ValidationError("Intent cannot be empty")
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Intent must be at least 10 characters long")
        return value.strip()

    def create(self, validated_data):
        """Create a new plugin generation request with file processing"""
        print("🚀 CREATE METHOD CALLED!")
        print(f"🚀 Validated data keys: {list(validated_data.keys())}")

        # Extract user if passed from view
        user = validated_data.pop('user', None)
        print(f"🚀 User: {user}")

        uploaded_file = validated_data['uploaded_file']
        print(f"🚀 Uploaded file: {uploaded_file}")
        print(f"🚀 File name: {uploaded_file.name}")
        print(f"🚀 File size: {uploaded_file.size}")

        # Extract file information
        original_filename = uploaded_file.name
        file_extension = os.path.splitext(original_filename)[1].lower()

        # Determine file type
        if file_extension == '.json':
            file_type = 'json'
        elif file_extension in ['.yaml', '.yml']:
            file_type = 'yaml'
        else:
            file_type = 'unknown'

        # Create the instance first (always save the file)
        print(f"🚀 Creating instance with service_name: {validated_data['service_name']}")
        instance = PluginGenerationRequest.objects.create(
            service_name=validated_data['service_name'],
            uploaded_file=uploaded_file,
            original_filename=original_filename,
            file_type=file_type,
            intent=validated_data['intent'],
            user=user,
            status='pending'
        )
        print(f"🚀 Instance created with ID: {instance.id}")

        # Parse uploaded file content (assume it's valid YAML/JSON)
        try:
            print(f"🔍 Starting to parse file: {original_filename} (type: {file_type})")

            # Read file content
            content = uploaded_file.read().decode('utf-8')
            print(f"🔍 File content length: {len(content)} characters")
            print(f"🔍 First 200 chars: {content[:200]}...")

            uploaded_file.seek(0)  # Reset file pointer for saving

            # Parse based on file type (assume valid format)
            if file_type == 'json':
                print("🔍 Parsing as JSON...")
                openapi_spec = json.loads(content)
            else:  # yaml or yml
                print("🔍 Parsing as YAML...")
                openapi_spec = yaml.safe_load(content)

            print(f"🔍 Parsed spec keys: {list(openapi_spec.keys()) if openapi_spec else 'None'}")

            # Save parsed spec
            instance.openapi_spec = openapi_spec
            instance.save()
            print(f"✅ Real OpenAPI spec parsed for {instance.service_name} ({file_type})")

        except Exception as e:
            # If parsing fails, save error but keep the record
            instance.status = 'failed'
            instance.error_message = f"Error parsing {file_type} file: {str(e)}"
            instance.save()
            print(f"❌ Parsing error: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")

        return instance


class PluginGenerationListSerializer(serializers.ModelSerializer):
    """Lightweight serializer for listing requests"""
    
    processing_duration = serializers.ReadOnlyField()
    
    class Meta:
        model = PluginGenerationRequest
        fields = [
            'id', 'service_name', 'original_filename', 'file_type', 'intent',
            'status', 'created_at', 'updated_at', 'processing_duration'
        ]
        read_only_fields = fields
