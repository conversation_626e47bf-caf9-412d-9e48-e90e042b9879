# Generated by Django 5.2 on 2025-07-07 04:04

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PluginGenerationRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('service_name', models.Char<PERSON>ield(help_text='Name of the service (e.g., VirusTotal)', max_length=255)),
                ('openapi_spec', models.J<PERSON>NField(help_text='Complete OpenAPI/Swagger specification')),
                ('intent', models.TextField(help_text="User's intent describing what endpoints they need")),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('testing', 'Testing')], default='pending', help_text='Current processing status', max_length=20)),
                ('generated_plugin_spec', models.J<PERSON><PERSON>ield(blank=True, help_text='Generated Bricklayer plugin specification', null=True)),
                ('test_results', models.JSONField(blank=True, help_text='Results from plugin testing', null=True)),
                ('error_message', models.TextField(blank=True, help_text='Error message if processing failed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processing_started_at', models.DateTimeField(blank=True, help_text='When processing started', null=True)),
                ('processing_completed_at', models.DateTimeField(blank=True, help_text='When processing completed (success or failure)', null=True)),
                ('user', models.ForeignKey(blank=True, help_text='User who created this request', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Plugin Generation Request',
                'verbose_name_plural': 'Plugin Generation Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProcessingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step', models.CharField(help_text='Processing step identifier', max_length=100)),
                ('message', models.TextField(help_text='Log message')),
                ('data', models.JSONField(blank=True, help_text='Additional structured data for this log entry', null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('request', models.ForeignKey(help_text='Associated plugin generation request', on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='openapi_generator.plugingenerationrequest')),
            ],
            options={
                'verbose_name': 'Processing Log',
                'verbose_name_plural': 'Processing Logs',
                'ordering': ['timestamp'],
            },
        ),
    ]
