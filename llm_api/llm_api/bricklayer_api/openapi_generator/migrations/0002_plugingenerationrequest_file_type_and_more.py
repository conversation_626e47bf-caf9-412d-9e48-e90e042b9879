# Generated by Django 5.2 on 2025-07-07 08:31

import openapi_generator.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('openapi_generator', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='plugingenerationrequest',
            name='file_type',
            field=models.CharField(blank=True, choices=[('json', 'JSON'), ('yaml', 'YAML'), ('yml', 'YML')], help_text='Type of the uploaded file', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='plugingenerationrequest',
            name='original_filename',
            field=models.CharField(blank=True, help_text='Original filename of the uploaded file', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='plugingenerationrequest',
            name='uploaded_file',
            field=models.FileField(blank=True, help_text='Uploaded OpenAPI/Swagger specification file (.json, .yaml, .yml)', null=True, upload_to=openapi_generator.models.openapi_upload_path),
        ),
        migrations.AlterField(
            model_name='plugingenerationrequest',
            name='openapi_spec',
            field=models.JSONField(blank=True, help_text='Parsed OpenAPI/Swagger specification (converted from uploaded file)', null=True),
        ),
    ]
