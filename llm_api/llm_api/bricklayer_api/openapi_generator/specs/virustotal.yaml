openapi: 3.0.3
info:
  title: VirusTotal Threat Intelligence API
  description: |
    VirusTotal's public API allows you to upload and scan files, submit and scan URLs, 
    access finished scan reports and make automatic comments on URLs and samples.
    
    This specification covers the most commonly used endpoints for threat intelligence operations.
  version: "3.0"
  contact:
    name: VirusTotal Support
    url: https://support.virustotal.com
  license:
    name: VirusTotal Terms of Service
    url: https://www.virustotal.com/gui/terms-of-service

servers:
  - url: https://www.virustotal.com/vtapi/v2
    description: VirusTotal API v2 (Legacy)
  - url: https://www.virustotal.com/vtapi/v3
    description: VirusTotal API v3 (Current)

security:
  - ApiKeyAuth: []

paths:
  # File Analysis Endpoints
  /files:
    post:
      summary: Upload and scan a file
      description: Submit a file for analysis by VirusTotal's antivirus engines
      tags:
        - File Analysis
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: File to be analyzed (max 650MB for public API)
      responses:
        '200':
          description: File upload successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'
        '400':
          description: Bad request
        '403':
          description: Forbidden - Invalid API key
        '413':
          description: File too large

  /files/{id}:
    get:
      summary: Get file analysis report
      description: Retrieve the analysis report for a file using its hash (MD5, SHA1, or SHA256)
      tags:
        - File Analysis
      parameters:
        - name: id
          in: path
          required: true
          description: File hash (MD5, SHA1, or SHA256)
          schema:
            type: string
            example: "d41d8cd98f00b204e9800998ecf8427e"
      responses:
        '200':
          description: File analysis report
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileReport'
        '404':
          description: File not found

  # URL Analysis Endpoints
  /urls:
    post:
      summary: Submit URL for scanning
      description: Submit a URL for analysis by VirusTotal's URL scanners
      tags:
        - URL Analysis
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                url:
                  type: string
                  format: uri
                  description: URL to be analyzed
                  example: "https://example.com"
      responses:
        '200':
          description: URL submission successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UrlSubmissionResponse'

  /urls/{id}:
    get:
      summary: Get URL analysis report
      description: Retrieve the analysis report for a URL
      tags:
        - URL Analysis
      parameters:
        - name: id
          in: path
          required: true
          description: URL identifier or base64-encoded URL
          schema:
            type: string
      responses:
        '200':
          description: URL analysis report
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UrlReport'

  # Domain Analysis Endpoints
  /domains/{domain}:
    get:
      summary: Get domain information
      description: Retrieve information about a domain including reputation data
      tags:
        - Domain Analysis
      parameters:
        - name: domain
          in: path
          required: true
          description: Domain name to analyze
          schema:
            type: string
            example: "example.com"
      responses:
        '200':
          description: Domain information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DomainReport'

  # IP Address Analysis Endpoints
  /ip_addresses/{ip}:
    get:
      summary: Get IP address information
      description: Retrieve information about an IP address including reputation data
      tags:
        - IP Analysis
      parameters:
        - name: ip
          in: path
          required: true
          description: IP address to analyze
          schema:
            type: string
            example: "*******"
      responses:
        '200':
          description: IP address information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IpReport'

  # Search Endpoints
  /intelligence/search:
    get:
      summary: Search VirusTotal intelligence
      description: Search for files, URLs, domains, and IPs using VirusTotal Intelligence queries
      tags:
        - Search
      parameters:
        - name: query
          in: query
          required: true
          description: Search query using VirusTotal Intelligence syntax
          schema:
            type: string
            example: "type:peexe size:200KB+ positives:5+"
        - name: order
          in: query
          description: Sort order for results
          schema:
            type: string
            enum: [first_submission_date, last_submission_date, positives, size]
            default: first_submission_date
        - name: limit
          in: query
          description: Maximum number of results to return
          schema:
            type: integer
            minimum: 1
            maximum: 300
            default: 20
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
      responses:
        '200':
          description: Search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'

  # Comments Endpoints
  /files/{id}/comments:
    get:
      summary: Get file comments
      description: Retrieve comments for a specific file
      tags:
        - Comments
      parameters:
        - name: id
          in: path
          required: true
          description: File hash
          schema:
            type: string
      responses:
        '200':
          description: File comments
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentsResponse'

    post:
      summary: Add comment to file
      description: Add a comment to a specific file
      tags:
        - Comments
      parameters:
        - name: id
          in: path
          required: true
          description: File hash
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  description: Comment text
                  maxLength: 4096
      responses:
        '200':
          description: Comment added successfully

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: x-apikey
      description: VirusTotal API key

  schemas:
    FileUploadResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "analysis"
            id:
              type: string
              description: Analysis ID for tracking
              example: "u-1234567890abcdef"

    FileReport:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "file"
            id:
              type: string
              description: File hash
            attributes:
              type: object
              properties:
                md5:
                  type: string
                  example: "d41d8cd98f00b204e9800998ecf8427e"
                sha1:
                  type: string
                  example: "da39a3ee5e6b4b0d3255bfef95601890afd80709"
                sha256:
                  type: string
                  example: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
                size:
                  type: integer
                  description: File size in bytes
                type_description:
                  type: string
                  description: File type description
                magic:
                  type: string
                  description: File magic header
                first_submission_date:
                  type: integer
                  description: Unix timestamp of first submission
                last_analysis_date:
                  type: integer
                  description: Unix timestamp of last analysis
                last_analysis_stats:
                  type: object
                  properties:
                    harmless:
                      type: integer
                    malicious:
                      type: integer
                    suspicious:
                      type: integer
                    undetected:
                      type: integer
                    timeout:
                      type: integer
                last_analysis_results:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      category:
                        type: string
                        enum: [harmless, malicious, suspicious, undetected, timeout]
                      engine_name:
                        type: string
                      engine_version:
                        type: string
                      result:
                        type: string
                      method:
                        type: string
                      engine_update:
                        type: string

    UrlSubmissionResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "analysis"
            id:
              type: string
              description: Analysis ID
              example: "u-1234567890abcdef"

    UrlReport:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "url"
            id:
              type: string
              description: URL identifier
            attributes:
              type: object
              properties:
                url:
                  type: string
                  format: uri
                first_submission_date:
                  type: integer
                last_analysis_date:
                  type: integer
                last_analysis_stats:
                  type: object
                  properties:
                    harmless:
                      type: integer
                    malicious:
                      type: integer
                    suspicious:
                      type: integer
                    undetected:
                      type: integer
                    timeout:
                      type: integer
                last_analysis_results:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      category:
                        type: string
                      result:
                        type: string
                      method:
                        type: string

    DomainReport:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "domain"
            id:
              type: string
              description: Domain name
            attributes:
              type: object
              properties:
                registrar:
                  type: string
                creation_date:
                  type: integer
                  description: Unix timestamp
                last_update_date:
                  type: integer
                  description: Unix timestamp
                reputation:
                  type: integer
                  description: Domain reputation score
                categories:
                  type: object
                  additionalProperties:
                    type: string
                last_analysis_stats:
                  type: object
                  properties:
                    harmless:
                      type: integer
                    malicious:
                      type: integer
                    suspicious:
                      type: integer
                    undetected:
                      type: integer

    IpReport:
      type: object
      properties:
        data:
          type: object
          properties:
            type:
              type: string
              example: "ip_address"
            id:
              type: string
              description: IP address
            attributes:
              type: object
              properties:
                country:
                  type: string
                  description: Country code
                as_owner:
                  type: string
                  description: ASN owner
                asn:
                  type: integer
                  description: Autonomous System Number
                reputation:
                  type: integer
                  description: IP reputation score
                last_analysis_stats:
                  type: object
                  properties:
                    harmless:
                      type: integer
                    malicious:
                      type: integer
                    suspicious:
                      type: integer
                    undetected:
                      type: integer

    SearchResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              id:
                type: string
              attributes:
                type: object
        meta:
          type: object
          properties:
            cursor:
              type: string
              description: Cursor for next page
            count:
              type: integer
              description: Number of results returned

    CommentsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: "comment"
              id:
                type: string
              attributes:
                type: object
                properties:
                  date:
                    type: integer
                    description: Unix timestamp
                  text:
                    type: string
                    description: Comment text
                  author:
                    type: string
                    description: Comment author

tags:
  - name: File Analysis
    description: Upload and analyze files for malware detection
  - name: URL Analysis
    description: Submit and analyze URLs for malicious content
  - name: Domain Analysis
    description: Analyze domain reputation and information
  - name: IP Analysis
    description: Analyze IP address reputation and information
  - name: Search
    description: Search VirusTotal intelligence database
  - name: Comments
    description: Manage comments on analyzed items