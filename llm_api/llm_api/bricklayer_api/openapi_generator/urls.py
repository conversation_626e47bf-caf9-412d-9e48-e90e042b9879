"""
URL configuration for the OpenAPI Plugin Generator app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import PluginGenerationViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'plugin-generation', PluginGenerationViewSet, basename='plugin-generation')

# App name for namespacing
app_name = 'openapi_generator'

# URL patterns
urlpatterns = [
    # API routes
    path('', include(router.urls)),
]
