"""
Celery tasks for async processing of plugin generation.
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

try:
    from celery import shared_task
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False
    # Create a dummy decorator for when Celery is not available
    def shared_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_plugin_generation(self, request_id: str) -> Optional[str]:
    """
    Async task for processing plugin generation requests.
    
    Args:
        request_id: UUID string of the plugin generation request
        
    Returns:
        Success message or None if failed
    """
    from .services import PluginGenerationService
    
    try:
        logger.info(f"Starting async processing for request {request_id}")
        PluginGenerationService.process_request(request_id)
        logger.info(f"Successfully completed processing for request {request_id}")
        return f"Successfully processed request {request_id}"
        
    except Exception as exc:
        logger.exception(f"Error in async processing for request {request_id}: {str(exc)}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_countdown = 60 * (2 ** self.request.retries)  # 60s, 120s, 240s
            logger.info(f"Retrying request {request_id} in {retry_countdown} seconds (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc, countdown=retry_countdown)
        else:
            logger.error(f"Max retries exceeded for request {request_id}")
            raise exc


@shared_task(bind=True, max_retries=2)
def cleanup_old_requests(self, days_old: int = 30) -> str:
    """
    Cleanup old plugin generation requests.
    
    Args:
        days_old: Number of days after which to delete requests
        
    Returns:
        Cleanup summary message
    """
    from django.utils import timezone
    from datetime import timedelta
    from .models import PluginGenerationRequest
    
    try:
        cutoff_date = timezone.now() - timedelta(days=days_old)
        
        # Delete old completed and failed requests
        old_requests = PluginGenerationRequest.objects.filter(
            created_at__lt=cutoff_date,
            status__in=['completed', 'failed']
        )
        
        count = old_requests.count()
        old_requests.delete()
        
        message = f"Cleaned up {count} old plugin generation requests older than {days_old} days"
        logger.info(message)
        return message
        
    except Exception as exc:
        logger.exception(f"Error in cleanup task: {str(exc)}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(exc=exc, countdown=300)  # Retry in 5 minutes
        else:
            raise exc
