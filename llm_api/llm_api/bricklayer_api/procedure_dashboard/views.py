import os
import logging
from typing import Literal
from pydantic import SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

# Temporarily commented out for testing - missing pantheon wheel file
# from blai.pantheon.agent_debrief.models import ProcedureRunDetails
# from blai.pantheon.agent_debrief.processor import (
#     AgentDebriefProcessor,
#     AgentDebriefProcessorSettings,
#     AgentDebriefProcessingRequest,
# )

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Step 1: Load Azure settings from env
class AzureOpenAIEnvSettings(BaseSettings):
    kind: Literal["AzureOpenAISettings"] = "AzureOpenAISettings"
    model_config = SettingsConfigDict(env_prefix="AZURE_OPENAI_")

    DEPLOYMENT: str
    API_BASE: str
    API_KEY: SecretStr
    API_VERSION: str = "2024-08-01-preview"

# Step 2: API view to process debrief
class ProcedureRunDashboardView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        try:
            logger.debug("📥 Incoming debrief data: %s", request.data)

            # Load settings from env vars
            env_settings = AzureOpenAIEnvSettings()

            # Build AI settings
            ai_settings_dict = {
                "kind": env_settings.kind,
                "DEPLOYMENT": env_settings.DEPLOYMENT,
                "ENDPOINT": env_settings.API_BASE,
                "API_KEY": env_settings.API_KEY.get_secret_value(),
                "API_VERSION": env_settings.API_VERSION
            }

            # Temporarily commented out - missing pantheon wheel file
            # procedure_details = ProcedureRunDetails(**request.data)
            # agent_input = AgentDebriefProcessingRequest(procedure_run_output=procedure_details)
            # processor = AgentDebriefProcessor(
            #     settings=AgentDebriefProcessorSettings(ai=ai_settings_dict)
            # )
            # result = processor(agent_input)

            # Temporary placeholder response
            result = {"message": "Pantheon module temporarily disabled", "status": "placeholder"}

            # Serialize result
            response_data = result  # already a dict for placeholder

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.exception("❌ Error generating agent debrief")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# class ProcedureRunDashboardView(APIView):
#     authentication_classes = []
#     permission_classes = []
#
#     def post(self, request):
#         try:
#             logger.debug("📥 Incoming dashboard data: %s", request.data)
#
#             # Temporarily return hardcoded dashboard response
#             return Response({
#                 "message": "Procedure Run Dashboard sent successfully",
#                 "dashDonaldResponse": {
#                     "id": "a201tmqbduno57611krvgova",
#                     "elements": [
#                         {
#                             "kind": "KeyValuePairListProps",
#                             "title": "Executive Summary",
#                             "items": [
#                                 {"title": "Alarm Date", "value": "May 15, 2025", "type": "Informational"},
#                                 {"title": "Source IP", "value": "************", "type": "Suspicious"},
#                                 {"title": "Target Server", "value": "app-server-03", "type": "Informational"},
#                                 {"title": "Rationale", "value": "Potential brute-force attack detected", "type": "Malicious"},
#                             ]
#                         },
#                         {
#                             "kind": "RichText",
#                             "title": "Key Findings",
#                             "content": "- Multiple failed SSH login attempts from the IP address ************ indicate a potential brute-force attack.\n- The risk includes unauthorized access, service disruption, and potential reputation damage.\n- Further investigation is needed to determine if the source IP is associated with known malicious activity."
#                         },
#                         {
#                             "kind": "TimelineCardProps",
#                             "title": "Children Tasks Timeline",
#                             "steps": [
#                                 {"label": "Explain the alert related to the failed SSH logins.", "timestamp": "2025-05-15T09:22:50Z"},
#                                 {"label": "Identify the indicators of compromise in the alert.", "timestamp": "2025-05-15T09:22:50Z"},
#                                 {"label": "Generate the Alert analysis report.", "timestamp": "2025-05-15T09:22:50Z"}
#                             ],
#                             "data": [
#                                 {"title": "Overall Status", "value": "Finished", "type": "Informational"},
#                                 {"title": "Recommendations Status", "value": "Pending", "type": "Informational"}
#                             ]
#                         }
#                     ]
#                 },
#                 "preSignedUrl": "https://ba-procedure-runs-dev.s3.us-east-1.amazonaws.com/dev/p2hp5tj7dv03yx86bqzl60sx/269/a201tmqbduno57611krvgova/f19fa642-79be-4478-8a97-ead680c7483f.json?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&..."
#             }, status=status.HTTP_200_OK)
#
#         except Exception as e:
#             logger.exception("❌ Error generating dashboard summary")
#             return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
