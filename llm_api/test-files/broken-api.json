{
  "openapi": "3.0.0",
  "info": {
    "title": "Broken API",
    "version": "1.0.0",
    "description": "This file has JSON syntax errors"
  },
  "servers": [
    {
      "url": "https://api.example.com"
    }
  ],
  "paths": {
    "/test": {
      "get": {
        "summary": "Test endpoint",
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    // This comment will break JSON parsing
    "/broken": {
      "post": {
        "summary": "Broken endpoint",
        "responses": {
          "201": {
            "description": "Created"
          }
        }
      }
    }
  }
  // Missing closing brace will also break parsing
