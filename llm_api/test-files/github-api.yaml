openapi: 3.0.0
info:
  title: GitHub API
  version: 1.0.0
  description: GitHub REST API for repository management
servers:
  - url: https://api.github.com
paths:
  /repos/{owner}/{repo}:
    get:
      summary: Get repository information
      description: Get details about a specific repository
      parameters:
        - name: owner
          in: path
          required: true
          schema:
            type: string
          description: Repository owner
        - name: repo
          in: path
          required: true
          schema:
            type: string
          description: Repository name
      responses:
        '200':
          description: Repository information
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  full_name:
                    type: string
                  private:
                    type: boolean
  /repos/{owner}/{repo}/issues:
    get:
      summary: List repository issues
      description: Get list of issues for a repository
      parameters:
        - name: owner
          in: path
          required: true
          schema:
            type: string
        - name: repo
          in: path
          required: true
          schema:
            type: string
        - name: state
          in: query
          required: false
          schema:
            type: string
            enum: [open, closed, all]
            default: open
      responses:
        '200':
          description: List of issues
    post:
      summary: Create an issue
      description: Create a new issue in the repository
      parameters:
        - name: owner
          in: path
          required: true
          schema:
            type: string
        - name: repo
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                body:
                  type: string
                labels:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Issue created
  /user/repos:
    get:
      summary: List user repositories
      description: Get repositories for the authenticated user
      parameters:
        - name: type
          in: query
          required: false
          schema:
            type: string
            enum: [all, owner, member]
            default: owner
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum: [created, updated, pushed, full_name]
            default: full_name
      responses:
        '200':
          description: List of repositories
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - BearerAuth: []
