openapi: 3.0.0
info:
  title: Broken API
  version: 1.0.0
  description: This file has YAML syntax errors
servers:
  - url: https://api.example.com
paths:
  /test:
    get:
      summary: Test endpoint
      responses:
        '200':
          description: Success
    # This line has invalid indentation and will cause YAML parsing to fail
  invalid_yaml_syntax_here: this is not properly indented
      another_broken_line: value
  /another-endpoint:
    post:
      summary: Another endpoint
      # Missing proper structure
      responses
        '201'
          description: Created
