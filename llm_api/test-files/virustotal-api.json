{"openapi": "3.0.0", "info": {"title": "VirusTotal API", "version": "3.0", "description": "VirusTotal API for cybersecurity analysis"}, "servers": [{"url": "https://www.virustotal.com/api/v3"}], "paths": {"/ip_addresses/{ip}": {"get": {"summary": "Get IP address analysis", "description": "Retrieve information about an IP address", "parameters": [{"name": "ip", "in": "path", "required": true, "schema": {"type": "string"}, "description": "IP address to analyze"}], "responses": {"200": {"description": "IP analysis results", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "attributes": {"type": "object", "properties": {"reputation": {"type": "integer"}, "country": {"type": "string"}}}}}}}}}}}}}, "/domains/{domain}": {"get": {"summary": "Get domain analysis", "description": "Retrieve information about a domain", "parameters": [{"name": "domain", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Domain to analyze"}], "responses": {"200": {"description": "Domain analysis results"}}}}, "/files/{id}": {"get": {"summary": "Get file analysis", "description": "Retrieve information about a file", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "File ID to analyze"}], "responses": {"200": {"description": "File analysis results"}}}}, "/users/{id}": {"get": {"summary": "Get user information", "description": "Retrieve user account information", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "User ID"}], "responses": {"200": {"description": "User information"}}}}}, "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-apikey"}}}, "security": [{"ApiKeyAuth": []}]}