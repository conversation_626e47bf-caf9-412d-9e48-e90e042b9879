FROM python:3.11

WORKDIR /deps

COPY ./requirements/common.txt ./requirements.txt

RUN pip install -r requirements.txt

COPY ./llm_api /deps/llm_api

RUN rm -rf /deps/bricklayer_api

WORKDIR /code

RUN apt update && apt install -y \
    jq \
    libpq-dev \
    gdal-bin \
    libgdal-dev



ENV GDAL_LIBRARY_PATH=/usr/lib/libgdal.so

COPY ./llm_api/bricklayer_api/requirements/common.txt ./requirements/common.txt
COPY ./llm_api/bricklayer_api/requirements/dev.txt ./requirements/prod.txt

# To be removed once private package index is available for pantheon
COPY ./llm_api/bricklayer_api/pantheon-1.0.0-py3-none-any.whl ./pantheon-1.0.0-py3-none-any.whl


RUN pip install -r /deps/requirements.txt -r requirements/prod.txt
RUN pip install structlog

COPY ./llm_api/bricklayer_api .

ENV PYTHONPATH="/deps"

EXPOSE 8001

CMD ["uvicorn", "--host", "0.0.0.0", "--port", "8001", "bricklayer_api.asgi:application"]
