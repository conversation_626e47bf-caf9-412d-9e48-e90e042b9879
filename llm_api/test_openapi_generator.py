#!/usr/bin/env python3
"""
Test script for the OpenAPI Plugin Generator integration.

This script demonstrates how to use the new Django app within the LLM API project.
"""

import requests
import json
import time
from typing import Dict, Any


def test_openapi_generator():
    """Test the OpenAPI generator functionality."""
    
    # Sample VirusTotal OpenAPI spec (simplified)
    virustotal_spec = {
        "openapi": "3.0.0",
        "info": {
            "title": "VirusTotal API",
            "version": "3.0",
            "description": "VirusTotal API for cybersecurity analysis"
        },
        "servers": [
            {"url": "https://www.virustotal.com/api/v3"}
        ],
        "paths": {
            "/ip_addresses/{ip}": {
                "get": {
                    "summary": "Get IP address analysis",
                    "description": "Retrieve information about an IP address",
                    "parameters": [
                        {
                            "name": "ip",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "IP address to analyze"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "IP analysis results"
                        }
                    }
                }
            },
            "/domains/{domain}": {
                "get": {
                    "summary": "Get domain analysis",
                    "description": "Retrieve information about a domain",
                    "parameters": [
                        {
                            "name": "domain",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "Domain to analyze"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Domain analysis results"
                        }
                    }
                }
            }
        },
        "components": {
            "securitySchemes": {
                "ApiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "x-apikey"
                }
            }
        },
        "security": [
            {"ApiKeyAuth": []}
        ]
    }
    
    # Test data
    test_payload = {
        "service_name": "VirusTotal",
        "openapi_spec": virustotal_spec,
        "intent": "I require IP and host related API endpoints for cybersecurity threat analysis. Focus on IP address reputation checking and domain analysis capabilities."
    }
    
    base_url = "http://localhost:8001"  # Django dev server
    api_url = f"{base_url}/api/openapi/plugin-generation/"
    
    print("🚀 Testing OpenAPI Plugin Generator Integration")
    print("=" * 50)
    
    try:
        # Test 1: Health check
        print("\n1. Testing health endpoint...")
        health_response = requests.get(f"{api_url}health/")
        if health_response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {health_response.json()}")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return
        
        # Test 2: Create plugin generation request
        print("\n2. Creating plugin generation request...")
        response = requests.post(
            api_url,
            json=test_payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 201:
            print("✅ Request created successfully")
            request_data = response.json()
            request_id = request_data['id']
            print(f"   Request ID: {request_id}")
            print(f"   Status: {request_data['status']}")
        else:
            print(f"❌ Failed to create request: {response.status_code}")
            print(f"   Error: {response.text}")
            return
        
        # Test 3: Check request status
        print("\n3. Checking request status...")
        status_response = requests.get(f"{api_url}{request_id}/")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"✅ Status retrieved: {status_data['status']}")
            
            # If completed, show results
            if status_data['status'] == 'completed':
                print("🎉 Plugin generation completed!")
                if status_data.get('generated_plugin_spec'):
                    print("   Generated plugin spec available")
                if status_data.get('test_results'):
                    print("   Test results available")
        else:
            print(f"❌ Failed to get status: {status_response.status_code}")
        
        # Test 4: Get processing logs
        print("\n4. Getting processing logs...")
        logs_response = requests.get(f"{api_url}{request_id}/logs/")
        if logs_response.status_code == 200:
            logs_data = logs_response.json()
            print(f"✅ Retrieved {len(logs_data['logs'])} log entries")
            for log in logs_data['logs'][-3:]:  # Show last 3 logs
                print(f"   {log['timestamp']}: {log['step']} - {log['message']}")
        else:
            print(f"❌ Failed to get logs: {logs_response.status_code}")
        
        # Test 5: Get statistics
        print("\n5. Getting statistics...")
        stats_response = requests.get(f"{api_url}stats/")
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print("✅ Statistics retrieved")
            print(f"   Total requests: {stats_data['total_requests']}")
            print(f"   Recent requests (24h): {stats_data['recent_requests_24h']}")
        else:
            print(f"❌ Failed to get stats: {stats_response.status_code}")
        
        print("\n🎯 Integration test completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the Django server is running on localhost:8001")
        print("   Run: python manage.py runserver 8001")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    test_openapi_generator()
