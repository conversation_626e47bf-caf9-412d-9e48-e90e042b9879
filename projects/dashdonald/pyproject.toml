[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "dashdonald"
version = "0.1.0"
description = "Dashdonald: a Django project powered by Bricklayer.ai"
authors = [{ name = "<PERSON><PERSON> Tolci", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11"

dependencies = [
    "Django>=4.2,<5.0",
#    "pantheon==0.3.0",
#    "hamptoncourt==0.2.0",
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.envs.dev]
dependencies = [
    "ipython",
    "pytest-django",
    "django-environ",
]

[tool.hatch.envs.default.scripts]
run = "python manage.py runserver 0.0.0.0:8000"
